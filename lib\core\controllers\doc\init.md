### init_task.dart
本文件主要编写了一些有关运行软件时，软件会进行的初始化工作。

#### 全局变脸
主页部分的位置，方便其他调用，用于点击“我的”界面后，跳转会“主页”。
`RxInt currentIndex = 0.obs;`

// 当前进入的角色界面，其中id是根据选择卡片里面设定的来
// 唯一用于gpt破限确定使用那个角色的破限词
// 云端：json传递角色id与对应的prompt
// 本地：角色id对应不同的界面，以及对应获取云端prompt，还用于判断该角色是否有自定义界面
`RxInt currentCharacterId = 0.obs;`

在初始化的时候，会被补全里面的内容，为的是，可以在软件运行的时候，获取所有角色的自定义界面信息，便于
之后任意角色根据角色id来读取。
```dart
  final RxMap<int, CharacterSetting> characterSettings =
      <int, CharacterSetting>{}.obs;
```

使用了get的方式，相当于是一个方法，调用之后会返回正常的数值。
```dart
  // 检测当前角色是否选择了自定义或者默认的界面
RxBool get isCurrentCharOnCustomPage =>
    characterSettings[currentCharacterId.value]?.isUsedCustomPage ??
        false.obs;
```


#### onInit
在软件运行时会自动调用。

初始化`characterSettings`的内容
`
    await _initializeCharacterSettings();
`
```dart
    // 根据初始的 character id 加载设置
await loadPageSettingsForCurrentCharacter();
```
```dart
// 从云端获取信息
Client re = Client();
re.getPrompt();
re.getModels();
```

#### _initializeCharacterSettings
解释起来有一些复杂的方法，初始化了`characterSettings`这个全局变量。
其中`characterSettings`用于储存多个角色信息对应的**自定义界面**相关的信息，类型为list。
储存的单个元素格式如下：
```dart
    CharacterSetting(
      id:0, // 角色ID 0 为糖糖
      isCustom: true, // 默认是否使用自定义界面
      savePageName: ConstData.isJine, // 储存是否使用自定义界面的变量名
    )
```

#### loadPageSettingsForCurrentCharacter
会根据`currentCharacterId`的数值来变化读取角色储存的自定义界面信息，其中`currentCharacterId`的值会在进入到
角色聊天界面的时候传递。

首先从`characterSettings`中根据当前角色的id获取其相关的配置信息，变量名为：charSetting。
其次再打开setting box，并且读取charSetting中对应的，储存名称为savePageName的变量名。
```dart
    final charSetting = characterSettings[currentCharacterId.value];
    final Box settingBox = await Hive.openBox(ConstData.settingBox);
    final storedValue = settingBox.get(charSetting.savePageName);
```
最后再把读取的数值传递到charSetting中，考虑到可能为null，所以默认为false
```dart
    // 从Hive读取的值可能为null，提供一个false作为默认值
    charSetting.isUsedCustomPage.value = storedValue ?? false;
```
这边只写了读取自定义界面相关的代码，储存部分写在了切换界面的数值。