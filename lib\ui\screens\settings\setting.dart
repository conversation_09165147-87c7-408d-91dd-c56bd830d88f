import 'package:aichat/core/controllers/page/login_controller.dart';
import 'package:aichat/core/services/announcement_service.dart';
import 'package:aichat/ui/screens/settings/theme_setting_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  late LoginController loginController;

  @override
  void initState() {
    super.initState();
    loginController = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: _buildAppBar(context),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          // const SizedBox(height: 16),
          // _buildSearchbar(),
          // const SizedBox(height: 24),
          // _buildSectionTitle('账号'),
          // _buildSettingsGroup([
          //   _buildSettingsItem(
          //     icon: Icons.security,
          //     title: '账号与安全',
          //     onTap: () {},
          //   ),
          //   _buildSettingsItem(
          //     icon: Icons.lock,
          //     title: '隐私设置',
          //     onTap: () {},
          //   ),
          //   _buildSettingsItem(
          //     icon: Icons.payment,
          //     title: '支付设置',
          //     onTap: () {},
          //   ),
          // ]),
          // const SizedBox(height: 24),
          _buildSectionTitle('通用'),
          _buildSettingsGroup([
            _buildSettingsItem(
              icon: Icons.palette_outlined,
              title: '背景设置',
              onTap: () => Get.to(() => const ThemeSettingsPage()),
            ),
            // {{ AURA-X: Add - 添加公告通知设置项. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
            _buildSettingsItem(
              icon: Icons.notifications,
              title: '公告通知',
              subtitle: '查看系统公告和通知',
              onTap: () => _handleAnnouncementTap(),
            ),
            _buildSettingsItem(
              icon: Icons.logout,
              title: '退出登录',
              onTap: () => _showLogoutDialog(context),
              isDestructive: true,
            ),
          ]),
        ],
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text('设置',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
      elevation: 0,
      backgroundColor: Colors.transparent,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      centerTitle: true,
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  Widget _buildSettingsGroup(List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: items,
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    String? subtitle,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive
            ? Theme.of(context).colorScheme.error
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDestructive
              ? Theme.of(context).colorScheme.error
              : Theme.of(context).colorScheme.onSurface,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            )
          : null,
      trailing: const Icon(
        Icons.chevron_right,
      ),
      onTap: onTap,
    );
  }

  Widget _buildSearchbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const TextField(
        decoration: InputDecoration(
          icon: Icon(Icons.search),
          hintText: '搜索',
          border: InputBorder.none,
        ),
      ),
    );
  }

  // {{ AURA-X: Add - 处理公告通知点击事件. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
  void _handleAnnouncementTap() async {
    // 显示加载提示
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // 手动显示公告
      await AnnouncementService.showAnnouncementsManually();
    } finally {
      // 关闭加载提示
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
    }
  }

  void _showLogoutDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('退出登录'),
        content: const Text('确定要退出当前账户吗？'),
        actions: [
          TextButton(
            onPressed: Get.back,
            child: Text(
              '取消',
              style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              loginController.logout();
            },
            child: Text(
              '退出',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
