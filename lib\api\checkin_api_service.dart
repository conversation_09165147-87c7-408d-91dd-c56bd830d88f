// {{ AURA-X: Add - 创建签到API服务类. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';

import '../data/const.dart';
import '../data/models/api_defult_model.dart';
import '../data/models/checkin_model.dart';
import '../data/models/checkin_history_response.dart';
import '../data/models/checkin_status_response.dart';
import 'http/dio_service.dart';

class CheckinApiService {
  final Dio _dio = DioService.instance;

  // 执行每日签到
  Future<CheckinModel> dailyCheckin() async {
    try {
      // 获取用户信息
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      String userName =
          await settingBox.get(ConstData.userName, defaultValue: '');
      String token = await settingBox.get(ConstData.keyValue, defaultValue: '');

      if (userName.isEmpty || token.isEmpty) {
        throw Exception('用户未登录或缺少必要信息');
      }

      // 发送签到请求
      Response response = await _dio.post(
        '/checkin/',
        data: {
          'username': userName,
          'token': token.replaceAll('sk-', ''), // 移除sk-前缀
        },
      );

      APIModel result = response.data;

      if (result.isSuccess && result.data != null) {
        // 解析签到结果
        CheckinModel checkinResult = CheckinModel.fromJson(result.data);
        print('签到成功: ${checkinResult.formattedRewardAmount}');
        return checkinResult;
      } else {
        throw Exception(result.errorMessage);
      }
    } catch (e) {
      print('签到请求失败: $e');
      if (e is DioException) {
        if (e.response?.data is APIModel) {
          APIModel errorResult = e.response!.data;
          throw Exception(errorResult.errorMessage);
        }
      }
      throw Exception('签到失败: ${e.toString()}');
    }
  }

  // 获取签到历史记录
  Future<CheckinHistoryResponse> getCheckinHistory({int limit = 30}) async {
    try {
      // 获取用户信息
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      String userName =
          await settingBox.get(ConstData.userName, defaultValue: '');
      String token = await settingBox.get(ConstData.keyValue, defaultValue: '');

      if (userName.isEmpty || token.isEmpty) {
        throw Exception('用户未登录或缺少必要信息');
      }

      // 发送获取历史记录请求
      Response response = await _dio.get(
        '/checkin/history',
        queryParameters: {'limit': limit},
        data: {
          'username': userName,
          'token': token.replaceAll('sk-', ''), // 移除sk-前缀
        },
      );

      APIModel result = response.data;

      if (result.isSuccess && result.data != null) {
        // 解析历史记录
        CheckinHistoryResponse historyResponse =
            CheckinHistoryResponse.fromJson(result.data);
        print('获取签到历史成功: 共${historyResponse.totalCheckinDays}天');
        return historyResponse;
      } else {
        throw Exception(result.errorMessage);
      }
    } catch (e, s) {
      print('获取签到历史失败: $e $s');
      if (e is DioException) {
        if (e.response?.data is APIModel) {
          APIModel errorResult = e.response!.data;
          throw Exception(errorResult.errorMessage);
        }
      }
      throw Exception('获取签到历史失败: ${e.toString()}');
    }
  }

  // 查询签到状态
  Future<CheckinStatusResponse> getCheckinStatus() async {
    try {
      // 获取用户信息
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      String userName =
          await settingBox.get(ConstData.userName, defaultValue: '');
      String token = await settingBox.get(ConstData.keyValue, defaultValue: '');

      if (userName.isEmpty || token.isEmpty) {
        throw Exception('用户未登录或缺少必要信息');
      }

      // 发送查询状态请求
      Response response = await _dio.get(
        '/checkin/status',
        data: {
          'username': userName,
          'token': token.replaceAll('sk-', ''), // 移除sk-前缀
        },
      );

      APIModel result = response.data;

      if (result.isSuccess && result.data != null) {
        // 解析状态信息
        CheckinStatusResponse statusResponse =
            CheckinStatusResponse.fromJson(result.data);
        print('获取签到状态成功: ${statusResponse.statusDescription}');
        return statusResponse;
      } else {
        throw Exception(result.errorMessage);
      }
    } catch (e) {
      print('获取签到状态失败: $e');
      if (e is DioException) {
        if (e.response?.data is APIModel) {
          APIModel errorResult = e.response!.data;
          throw Exception(errorResult.errorMessage);
        }
      }
      throw Exception('获取签到状态失败: ${e.toString()}');
    }
  }
}
