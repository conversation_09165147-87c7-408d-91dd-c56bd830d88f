// {{ AURA-X: Add - 创建签到记录数据模型类. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
class CheckinModel {
  final int? checkinId;
  final String checkinDate;
  final double rewardAmount;
  final String? checkinTime;

  CheckinModel({
    this.checkinId,
    required this.checkinDate,
    required this.rewardAmount,
    this.checkinTime,
  });

  factory CheckinModel.fromJson(Map<String, dynamic> json) {
    return CheckinModel(
      checkinId: json['checkin_id'],
      checkinDate: json['checkin_date'] ?? '',
      rewardAmount: (json['reward_amount'] ?? 0.0).toDouble(),
      checkinTime: json['checkin_time'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'checkin_id': checkinId,
      'checkin_date': checkinDate,
      'reward_amount': rewardAmount,
      'checkin_time': checkinTime,
    };
  }

  // 获取格式化的签到时间
  String get formattedCheckinTime {
    if (checkinTime == null) return '未知时间';
    try {
      final dateTime = DateTime.parse(checkinTime!);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '未知时间';
    }
  }

  // 获取格式化的签到日期
  String get formattedCheckinDate {
    try {
      final dateTime = DateTime.parse(checkinDate);
      return '${dateTime.month}月${dateTime.day}日';
    } catch (e) {
      return checkinDate;
    }
  }

  // 获取奖励金额的格式化显示
  String get formattedRewardAmount {
    return '${rewardAmount.toStringAsFixed(2)}额度';
  }
}
