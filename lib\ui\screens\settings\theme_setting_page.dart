import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../theme/chat_theme.dart';

class ThemeSettingsPage extends StatefulWidget {
  const ThemeSettingsPage({super.key});

  @override
  State<ThemeSettingsPage> createState() => _ThemeSettingsPageState();
}

class _ThemeSettingsPageState extends State<ThemeSettingsPage> {
  final RxString selectedTheme = ''.obs;
  static const _themes = ['default', 'jine', 'bear', 'kuromi'];

  @override
  void initState() {
    super.initState();
    ChatThemeManager.loadThemeForCharacter().then((theme) {
      selectedTheme.value = theme.name;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('主题设置',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600)),
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(() {
              if (selectedTheme.value.isEmpty) {
                return const SizedBox.shrink();
              }
              final themeInfo =
                  ChatThemeManager.getThemeDisplayInfo(selectedTheme.value);
              final displayName =
                  themeInfo['displayName'] ?? selectedTheme.value;
              return Padding(
                padding: const EdgeInsets.fromLTRB(8, 0, 8, 24),
                child: RichText(
                  text: TextSpan(
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    children: [
                      const TextSpan(text: '当前主题: '),
                      TextSpan(
                        text: displayName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
            ..._themes.map((theme) => _buildThemeCard(context, theme)),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeCard(BuildContext context, String themeName) {
    final themeInfo = ChatThemeManager.getThemeDisplayInfo(themeName);

    return Obx(() {
      final isSelected = selectedTheme.value == themeName;
      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: _cardDecoration(context, isSelected),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            selectedTheme.value = themeName;
            ChatThemeManager.saveCharacterTheme(selectedTheme.value);
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildThemePreview(context, themeInfo),
                const SizedBox(width: 16),
                Expanded(child: _buildThemeInfo(context, themeInfo)),
                _buildSelectionIndicator(context, isSelected),
              ],
            ),
          ),
        ),
      );
    });
  }

  BoxDecoration _cardDecoration(BuildContext context, bool isSelected) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surfaceContainer,
      borderRadius: BorderRadius.circular(16),
      border: Border.all(
        color: isSelected
            ? Theme.of(context).primaryColor
            : Theme.of(context).colorScheme.outlineVariant,
        width: isSelected ? 2 : 1,
      ),
      boxShadow: [
        BoxShadow(
          color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildThemeInfo(BuildContext context, Map<String, dynamic> themeInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              themeInfo['displayName'],
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            if (themeInfo['characterOnly'] != null) ...[
              const SizedBox(width: 8),
              _buildExclusiveTag(context),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Text(
          themeInfo['description'],
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        if (themeInfo['characterOnly'] != null) ...[
          const SizedBox(height: 4),
          Text(
            '仅适用于角色：${themeInfo['characterOnly']}',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildExclusiveTag(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '专属',
        style: TextStyle(
          fontSize: 10,
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSelectionIndicator(BuildContext context, bool isSelected) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: isSelected
              ? Theme.of(context).primaryColor
              : Theme.of(context).colorScheme.outline,
          width: 2,
        ),
        color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
      ),
      child: isSelected
          ? Icon(
              Icons.check,
              size: 16,
              color: Theme.of(context).colorScheme.onPrimary,
            )
          : null,
    );
  }

  Widget _buildThemePreview(
      BuildContext context, Map<String, dynamic> themeInfo) {
    return GestureDetector(
      onTap: () {
        if (themeInfo['previewImage'] != null) {
          _showPreviewDialog(context, themeInfo);
        }
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(11),
          child: themeInfo['previewImage'] != null
              ? Image.asset(
                  themeInfo['previewImage'],
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => _buildImagePlaceholder(
                      context, Icons.image_not_supported),
                )
              : _buildImagePlaceholder(context, Icons.palette),
        ),
      ),
    );
  }

  Widget _buildImagePlaceholder(BuildContext context, IconData icon) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        icon,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        size: 24,
      ),
    );
  }

  void _showPreviewDialog(
      BuildContext context, Map<String, dynamic> themeInfo) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: GestureDetector(
          onTap: Get.back,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.9,
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildCloseButton(),
                _buildPreviewImage(context, themeInfo),
                _buildThemeNameLabel(themeInfo),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCloseButton() {
    return Align(
      alignment: Alignment.topRight,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: const BoxDecoration(
          color: Colors.black54,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: Get.back,
        ),
      ),
    );
  }

  Widget _buildPreviewImage(
      BuildContext context, Map<String, dynamic> themeInfo) {
    return Flexible(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 20,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Image.asset(
            themeInfo['previewImage'],
            fit: BoxFit.contain,
            errorBuilder: (_, __, ___) => Container(
              width: 200,
              height: 200,
              color: Theme.of(context).colorScheme.surfaceVariant,
              child: Icon(
                Icons.image_not_supported,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 48,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThemeNameLabel(Map<String, dynamic> themeInfo) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black54,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        themeInfo['displayName'],
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
