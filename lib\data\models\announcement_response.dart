// {{ AURA-X: Add - 创建公告响应数据模型类. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
import 'announcement_model.dart';

class AnnouncementResponse {
  final List<AnnouncementModel> announcements;
  final int total;
  final AnnouncementConfig config;

  AnnouncementResponse({
    required this.announcements,
    required this.total,
    required this.config,
  });

  factory AnnouncementResponse.fromJson(Map<String, dynamic> json) {
    var announcementsList = json['announcements'] as List? ?? [];
    List<AnnouncementModel> announcements = announcementsList
        .map((item) => AnnouncementModel.fromJson(item))
        .toList();

    return AnnouncementResponse(
      announcements: announcements,
      total: json['total'] ?? 0,
      config: AnnouncementConfig.fromJson(json['config'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'announcements': announcements.map((item) => item.toJson()).toList(),
      'total': total,
      'config': config.toJson(),
    };
  }
}

class AnnouncementConfig {
  final List<String> supportedTypes;
  final int maxDisplay;

  AnnouncementConfig({
    required this.supportedTypes,
    required this.maxDisplay,
  });

  factory AnnouncementConfig.fromJson(Map<String, dynamic> json) {
    var typesList =
        json['supported_types'] as List? ?? ['info', 'warning', 'urgent'];
    List<String> supportedTypes = typesList.cast<String>();

    return AnnouncementConfig(
      supportedTypes: supportedTypes,
      maxDisplay: json['max_display'] ?? 10,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'supported_types': supportedTypes,
      'max_display': maxDisplay,
    };
  }
}
