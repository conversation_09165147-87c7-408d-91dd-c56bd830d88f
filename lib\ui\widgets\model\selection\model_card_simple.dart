// {{ AURA-X: Add - 创建简化版模型卡片组件. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/page/profile_controller.dart';
import '../../../../ui/widgets/toast/toast.dart';
import '../shared/model_utils.dart';

class ModelCardSimple extends StatelessWidget {
  final dynamic model;
  final ProfileController controller;

  const ModelCardSimple({
    Key? key,
    required this.model,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final modelName = ModelUtils.getModelName(model);
    final inputPrice = ModelUtils.getInputPrice(model);
    final outputPrice = ModelUtils.getOutputPrice(model);
    final tags = ModelUtils.getTags(model);
    final mainTags = ModelUtils.getMainTags(tags, maxCount: 2);

    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: ModelUtils.isCurrentModel(model, controller.currentModelName.value)
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.outlineVariant,
              width: 1.5,
            ),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(24),
            onTap: () => _selectModel(context),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 顶部区域：模型名称和状态
                  _buildHeader(context),
                  
                  const SizedBox(height: 12),
                  
                  // 价格信息
                  _buildPriceSection(context, inputPrice, outputPrice),
                  
                  const SizedBox(height: 12),
                  
                  // 主要标签
                  if (mainTags.isNotEmpty) _buildTagsSection(mainTags),
                  
                  const Spacer(),
                  
                  // 底部提示
                  _buildBottomHint(context),
                ],
              ),
            ),
          ),
        ));
  }

  Widget _buildHeader(BuildContext context) {
    final modelName = ModelUtils.getModelName(model);
    final isSelected = ModelUtils.isCurrentModel(model, controller.currentModelName.value);

    return Row(
      children: [
        Expanded(
          child: Text(
            modelName,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.onSurface,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (isSelected)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '使用中',
              style: TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPriceSection(BuildContext context, double inputPrice, double outputPrice) {
    return Row(
      children: [
        if (inputPrice == 0.0 && outputPrice == 0.0)
          ModelUtils.buildFreeTag()
        else
          ModelUtils.buildPriceContainer(inputPrice, outputPrice, simplified: true),
      ],
    );
  }

  Widget _buildTagsSection(List<String> mainTags) {
    return ModelUtils.buildTagList(
      mainTags,
      compact: true,
      spacing: 6,
    );
  }

  Widget _buildBottomHint(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.touch_app,
          size: 14,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Text(
          '点击选择模型',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  void _selectModel(BuildContext context) {
    final modelName = ModelUtils.getModelName(model);
    controller.setCurrentModel(model);
    Toast.showSuccess('切换成功', '当前模型：$modelName');
  }
}
