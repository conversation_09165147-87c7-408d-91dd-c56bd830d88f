### data_controller.dart
本文件主要编写了初始化box的方式，其中的box会在软件启动时运行，并且运行软件时都可以在其他方法中直接被调用。

#### onInit
`GetxController`特有的生命周期函数，会在函数初始化的时候自动触发。
hive box的初始化和调用方式很简单，首先await open一个box，之后再进行赋值即可。
```dart
    await Hive.openBox(ConstData.settingBox);
    settingBox = Hive.box(ConstData.settingBox);
```
这边初始化的box为setting box，使用键值对的方式在里面进行储存各种东西，并非只是有关设置的内容。

#### initMessageBox
这边的init和onInit不同。初始化的内容为储存ai聊天信息的内容。
需要传递一个id，以读取打开特定名称的box。
其中调用的位置为聊天界面的初始化函数里。

#### addMessages
添加信息，需要传递的内容是用于显示聊天列表的对话模型MessageModel类信息。
其中添加是指把该信息转换为json，然后再储存到本地
**软件两种不同的信息储存类**
第一种为`MessageModel`类的用于显示*上下文*的信息，使用hive进行储存，储存名称为`ConstData.allCharactersBoxes`对应
的角色名称。
第二种为ai上下记录文的`ChatMessage`，可以组成list传递给ai，但是该类型不会进行储存到本地，它的获取方式为通过`MessageModel`进行
转换得到。

#### rewriteMessages
修改信息，一个略显复杂的功能，设计思路如下：
在一个信息列表中，如果想要修改信息A，那么用户直观的体验是：首先选择好需要修改的信息，其次进行编辑，最后保存编辑即可。
在程序方面的设计思路是：在用户选择了修改的信息后，将该信息从聊天与其底下的所有聊天记录都从删除，直到用户编辑保存新的信息，
再把新的信息添加进入聊天列表中。

代码部分主要只写了删除信息位置的功能，修改信息后的添加部分在外界调用addMessage即可。

其他想法：
如果未来有需求，可以试着把这个功能拆分开，一个是修改单条信息，一个是从某处开始重新对话。
#### cleanMessages
删除box里面的所有信息

#### getMessages
hive box储存聊天信息时，是先把`MessageModel`类转换为json，然后在储存进去所以读取的时候，也需要去把json转换为`MessageModel`
类。













