import 'dart:async';
import 'package:aichat/ai/until.dart';
import 'package:aichat/utils/performance_tracker.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:langchain/langchain.dart';
import 'package:langchain_openai/langchain_openai.dart';

import '../core/controllers/character_controller.dart';
import '../core/controllers/messages_controller.dart';
import '../data/const.dart';
import 'ai_error.dart';

class Gpt {
  /// 需要完成的任务
  /// 1. 在本地读取key
  /// 2. 在api接口处读取json
  /// 3. ai交互类
  /// 4. 储存用户发送内容以及ai返回内容

  // chatbot
  late ChatOpenAI chatbot;
  // 获取用户信息控制器，为了能够获取到当前用户的文本信息
  MessagesController messagesController = Get.find();
  // 初始化控制器，需要用到里面的当前角色id
  CharacterController initTask = Get.find();
  // 处理上下文的类，需要传入用户的上下文聊天记录，会返回修改后的聊天记录
  late JailBreak jailBreak;
  // 模型最大token
  late int maxTokens;
  // {{ AURA-X: Add - 添加预加载状态标记，避免重复初始化. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  // JailBreak是否已预加载
  bool _jailBreakPreloaded = false;

  // {{ AURA-X: Add - 添加流式传输控制字段. Approval: 寸止(ID:2025-08-03T23:30:00+08:00). }}
  /// 流式传输控制
  StreamSubscription<ChatResult>? _streamSubscription;
  bool _isStreamActive = false;

  // 初始化gpt相关的部分，获取到所有prompt，以及密钥相关的部分
  // {{ AURA-X: Modify - 优化初始化流程，利用已存在的box避免重复打开. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  initGPT() async {
    // 利用DataController中已初始化的settingBox，避免重复打开
    Box settingBox;
    try {
      settingBox = Hive.box(ConstData.settingBox);
    } catch (e) {
      // 如果box未打开，则打开它
      await Hive.openBox(ConstData.settingBox);
      settingBox = Hive.box(ConstData.settingBox);
    }

    Map? chatModel = await settingBox.get(ConstData.currentModel);
    String? apiKey = await settingBox.get(ConstData.keyValue);
    String? baseUrl = await settingBox.get(ConstData.baseUrl) + '/v1';
    maxTokens = chatModel?['maxToken'];

    print('当前gpt信息：$baseUrl, $apiKey');

    // 初始化赋值
    chatbot = ChatOpenAI(
        apiKey: apiKey,
        baseUrl: baseUrl.toString(),
        defaultOptions:
            ChatOpenAIOptions(temperature: 1.0, model: chatModel?['name']));
  }

  // {{ AURA-X: Add - 添加JailBreak预加载方法，避免首次发送时延迟. Approval: 寸止(ID:2025-08-03T17:09:22+08:00). }}
  // 预加载JailBreak数据，在聊天界面初始化时调用
  Future<void> preloadJailBreak() async {
    if (_jailBreakPreloaded) return; // 避免重复加载

    try {
      jailBreak = JailBreak(characterId: initTask.currentCharacterId.value);
      await jailBreak.initJB();
      _jailBreakPreloaded = true;
      print('JailBreak数据预加载完成');
    } catch (e) {
      print('JailBreak预加载失败: $e');
      // 预加载失败不影响正常流程，首次发送时会重新尝试
    }
  }

  // 从本地获取所有对话内容
  // {{ AURA-X: Add - 添加详细性能诊断，追踪getHistory方法的执行流程. Approval: 寸止(ID:2025-08-03T17:13:59+08:00). }}
  Future<List<ChatMessage>> getHistory() async {
    PerformanceTracker.start('getHistory_total');
    DetailedLogger.enter(
        'Gpt.getHistory', {'jailBreakPreloaded': _jailBreakPreloaded});

    try {
      /// 获取到所有ai与用户的交互信息后，即可开始加入破限内容
      /// 如果JailBreak未预加载，则进行初始化（兜底逻辑）
      if (!_jailBreakPreloaded) {
        PerformanceTracker.start('jailbreak_init_fallback');
        DetailedLogger.async('JailBreak.init', 'starting_fallback');
        jailBreak = JailBreak(characterId: initTask.currentCharacterId.value);
        await jailBreak.initJB();
        _jailBreakPreloaded = true;
        PerformanceTracker.stop('jailbreak_init_fallback');
        PerformanceTracker.checkpoint(
            'getHistory_total', 'jailbreak_initialized');
        DetailedLogger.async('JailBreak.init', 'completed_fallback');
      } else {
        PerformanceTracker.checkpoint(
            'getHistory_total', 'jailbreak_already_loaded');
        DetailedLogger.state('JailBreak', 'preloaded', true);
      }

      PerformanceTracker.start('jailbreak_history');
      DetailedLogger.async('jailBreak.jbHistory', 'starting');
      List<ChatMessage> i = jailBreak.jbHistory(
          messagesController.chatMessagesToGPT.value as List<ChatMessage>);
      PerformanceTracker.stop('jailbreak_history');
      PerformanceTracker.checkpoint(
          'getHistory_total', 'jailbreak_history_processed');
      DetailedLogger.async('jailBreak.jbHistory', 'completed');
      DetailedLogger.state('jbHistory', 'size', i.length);

      // {{ AURA-X: Modify - 使用异步Token计算，避免UI线程阻塞. Approval: 寸止(ID:2025-08-03T21:45:00+08:00). }}
      // 异步计算对话token，避免阻塞UI线程
      PerformanceTracker.start('token_calculation');
      DetailedLogger.async('Tokens.checkTokenCountAsync', 'starting');

      // 设置计算状态，改善用户体验
      messagesController.isCalculatingTokens.value = true;

      var jbHistory = await Tokens.checkTokenCountAsync(i, maxTokens);

      // 重置计算状态
      messagesController.isCalculatingTokens.value = false;

      PerformanceTracker.stop('token_calculation');
      PerformanceTracker.checkpoint('getHistory_total', 'token_calculated');
      DetailedLogger.async('Tokens.checkTokenCountAsync', 'completed');
      DetailedLogger.state('finalHistory', 'size', jbHistory.length);

      return jbHistory;
    } catch (e, stackTrace) {
      DetailedLogger.error('Gpt.getHistory', e.toString(), stackTrace);
      // 确保在异常情况下也重置计算状态
      messagesController.isCalculatingTokens.value = false;
      rethrow;
    } finally {
      final totalTime = PerformanceTracker.stop('getHistory_total');
      DetailedLogger.exit('Gpt.getHistory', 'total_time=${totalTime}ms');
    }
  }

  // 获取ai回复
  // {{ AURA-X: Add - 添加详细性能诊断，追踪getReply方法的执行流程. Approval: 寸止(ID:2025-08-03T17:13:59+08:00). }}
  Future getReply() async {
    PerformanceTracker.start('getReply_total');
    DetailedLogger.enter('Gpt.getReply');

    try {
      /// 获取历史记录
      PerformanceTracker.start('getHistory');
      DetailedLogger.async('getHistory', 'starting');
      List<ChatMessage> history = await getHistory();
      PerformanceTracker.stop('getHistory');
      PerformanceTracker.checkpoint('getReply_total', 'history_loaded');
      DetailedLogger.async('getHistory', 'completed');
      DetailedLogger.state('history', 'size', history.length);

      /// 格式化历史记录，并且传递给ai
      // ai正在回复中
      messagesController.isSending.value = true;
      DetailedLogger.state('MessagesController', 'isSending', true);
      PerformanceTracker.checkpoint('getReply_total', 'sending_state_set');

      PerformanceTracker.start('chatbot_invoke');
      DetailedLogger.async('chatbot.invoke', 'starting');
      DetailedLogger.network('POST', 'OpenAI_API', 'sending_request');
      ChatResult aiReply = await chatbot.invoke(PromptValue.chat(history));
      PerformanceTracker.stop('chatbot_invoke');
      PerformanceTracker.checkpoint('getReply_total', 'ai_reply_received');
      DetailedLogger.async('chatbot.invoke', 'completed');
      DetailedLogger.network('POST', 'OpenAI_API', 'response_received');
      DetailedLogger.state(
          'aiReply', 'contentLength', aiReply.output.content.toString().length);

      /// 储存ai信息
      PerformanceTracker.start('create_ai_message');
      MessageModel message = MessageModel(
          // todo 未来根据当前的角色来更换头像
          avatar: 'assets/background/jine_avatar.jpg',
          id: 2,
          ownerType: OwnerType.receiver,
          content: aiReply.output.content.toString(),
          createdAt: ConstData.getTime());
      PerformanceTracker.stop('create_ai_message');
      PerformanceTracker.checkpoint('getReply_total', 'ai_message_created');
      DetailedLogger.state('MessageModel', 'created', 'ai_response');

      /// 调用信息管理器里面的功能去添加信息，该功能会同时把信息本地化
      PerformanceTracker.start('add_ai_message');
      DetailedLogger.async(
          'messagesController.addMessage', 'starting_ai_message');
      await messagesController.addMessage(message);
      PerformanceTracker.stop('add_ai_message');
      PerformanceTracker.checkpoint('getReply_total', 'ai_message_added');
      DetailedLogger.async(
          'messagesController.addMessage', 'completed_ai_message');
    } catch (e, stackTrace) {
      DetailedLogger.error('Gpt.getReply', e.toString(), stackTrace);
      DetailedLogger.network('POST', 'OpenAI_API', 'error');
      // 使用AIErrorHandler处理异常
      AIErrorHandler.handleError(e, Get.context!);
      print(e);
    } finally {
      final totalTime = PerformanceTracker.stop('getReply_total');
      DetailedLogger.exit('Gpt.getReply', 'total_time=${totalTime}ms');

      // 如果总时间超过2000ms，打印详细报告
      if (totalTime > 2000) {
        DetailedLogger.async(
            'PerformanceReport', 'printing_due_to_slow_ai_response');
        PerformanceTracker.printReport();
      }
    }
  }

  // {{ AURA-X: Add - 添加流式回复方法，支持实时显示AI回复内容. Approval: 寸止(ID:2025-08-03T23:30:00+08:00). }}
  /// 获取流式AI回复
  Future getStreamReply() async {
    PerformanceTracker.start('getStreamReply_total');
    DetailedLogger.enter('Gpt.getStreamReply');

    try {
      /// 获取历史记录
      PerformanceTracker.start('getHistory_stream');
      DetailedLogger.async('getHistory', 'starting_stream');
      List<ChatMessage> history = await getHistory();
      PerformanceTracker.stop('getHistory_stream');
      PerformanceTracker.checkpoint('getStreamReply_total', 'history_loaded');
      DetailedLogger.async('getHistory', 'completed_stream');
      DetailedLogger.state('history', 'size', history.length);

      /// 设置发送状态
      messagesController.isSending.value = true;
      DetailedLogger.state('MessagesController', 'isSending', true);
      PerformanceTracker.checkpoint(
          'getStreamReply_total', 'sending_state_set');

      /// 创建流式消息
      PerformanceTracker.start('create_streaming_message');
      MessageModel streamingMessage =
          await messagesController.createStreamingMessage();
      PerformanceTracker.stop('create_streaming_message');
      PerformanceTracker.checkpoint(
          'getStreamReply_total', 'streaming_message_created');
      DetailedLogger.state(
          'StreamingMessage', 'created', 'id=${streamingMessage.id}');

      /// 开始流式传输
      _isStreamActive = true;
      PerformanceTracker.start('chatbot_stream');
      DetailedLogger.async('chatbot.stream', 'starting');
      DetailedLogger.network('POST', 'OpenAI_API_Stream', 'sending_request');

      final stream = chatbot.stream(PromptValue.chat(history));

      _streamSubscription = stream.listen(
        (ChatResult chunk) {
          if (_isStreamActive) {
            final content = chunk.output.content.toString();
            if (content.isNotEmpty) {
              // 更新流式内容
              messagesController.updateStreamingContent(content);
              DetailedLogger.state(
                  'StreamChunk', 'received', 'length=${content.length}');
            }
          }
        },
        onError: (error, stackTrace) {
          DetailedLogger.error(
              'Gpt.getStreamReply.stream', error.toString(), stackTrace);
          DetailedLogger.network('POST', 'OpenAI_API_Stream', 'error');
          messagesController.handleStreamingError(error.toString());
          _isStreamActive = false;
        },
        onDone: () async {
          PerformanceTracker.stop('chatbot_stream');
          PerformanceTracker.checkpoint(
              'getStreamReply_total', 'stream_completed');
          DetailedLogger.async('chatbot.stream', 'completed');
          DetailedLogger.network(
              'POST', 'OpenAI_API_Stream', 'response_completed');

          if (_isStreamActive) {
            // 完成流式传输
            await messagesController.completeStreaming();
            DetailedLogger.state('StreamingMessage', 'completed', 'success');
          }

          _isStreamActive = false;
          messagesController.isSending.value = false;
          DetailedLogger.state('MessagesController', 'isSending', false);
        },
      );
    } catch (e, stackTrace) {
      DetailedLogger.error('Gpt.getStreamReply', e.toString(), stackTrace);
      DetailedLogger.network('POST', 'OpenAI_API_Stream', 'error');
      messagesController.handleStreamingError(e.toString());
      _isStreamActive = false;

      // 使用AIErrorHandler处理异常
      AIErrorHandler.handleError(e, Get.context!);
      print(e);
    } finally {
      final totalTime = PerformanceTracker.stop('getStreamReply_total');
      DetailedLogger.exit('Gpt.getStreamReply', 'total_time=${totalTime}ms');

      // 如果总时间超过2000ms，打印详细报告
      if (totalTime > 2000) {
        DetailedLogger.async(
            'PerformanceReport', 'printing_due_to_slow_stream_response');
        PerformanceTracker.printReport();
      }
    }
  }

  // 停止获取回复
  close() {
    // 停止流式传输
    if (_streamSubscription != null) {
      _streamSubscription!.cancel();
      _streamSubscription = null;
      _isStreamActive = false;
      DetailedLogger.state('StreamSubscription', 'cancelled', 'user_requested');
    }

    chatbot.close();
  }
}
