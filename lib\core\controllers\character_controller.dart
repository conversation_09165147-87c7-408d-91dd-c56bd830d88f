import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_rx/src/rx_workers/rx_workers.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:hive/hive.dart';

import '../../data/const.dart';
import '../../data/models/character_setting.dart';

class CharacterController extends GetxController {
  // 主页部分的位置，方便其他调用
  RxInt currentIndex = 0.obs;

  // 当前进入的角色界面，其中id是根据选择卡片里面设定的来
  // 唯一用于gpt破限确定使用那个角色的破限词
  // 云端：json传递角色id与对应的prompt
  // 本地：角色id对应不同的界面，以及对应获取云端prompt，还用于判断该角色是否有自定义界面
  RxInt currentCharacterId = 0.obs;

  final RxMap<int, AICharacter> characterSettings = <int, AICharacter>{}.obs;

  // 检测当前角色是否选择了自定义或者默认的界面
  RxBool get isCurrentCharOnCustomPage =>
      characterSettings[currentCharacterId.value]?.isUsedCustomPage ??
      false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeCharacterSettings();
  }

  /// 初始化所有角色的基础设置
  /// todo 未来可以从配置文件或服务器动态加载
  Future<void> _initializeCharacterSettings() async {
    // 按照循序添加不同的角色，其中顺序就是角色id
    characterSettings.assignAll(ConstData.allCharacters.asMap());
  }
}
