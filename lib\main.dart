import 'package:aichat/core/controllers/character_controller.dart';
import 'package:aichat/ui/screens/account/profile.dart';
import 'package:aichat/ui/screens/home.dart';
import 'package:aichat/ui/screens/test.dart';
import 'package:aichat/ui/theme/light_dark/theme.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:toastification/toastification.dart';

import 'core/binding/all_binding.dart';
import 'core/controllers/page/login_controller.dart';
import 'core/routes/all_routes.dart';
import 'data/const.dart';

Future<void> main() async {
  // hive部分初始化
  WidgetsFlutterBinding.ensureInitialized();
  Hive.initFlutter();
  final appDocumentDirectory = await getApplicationDocumentsDirectory();
  Hive.init(appDocumentDirectory.path);

  runApp(MyApp());
}

class MyApp extends GetView<CharacterController> {
  List pages = [
    HomePage(),
    ProfilePage(),
    TestPage(),
  ];

  Widget build(BuildContext context) {
    return ToastificationWrapper(
        child: GetMaterialApp(
      home: Scaffold(
        body: Obx(() => pages[controller.currentIndex.value]),
        bottomNavigationBar: Obx(() => BottomNavigationBar(
              items: const [
                BottomNavigationBarItem(
                    icon: Icon(Icons.home_filled), label: '主页'),
                BottomNavigationBarItem(
                    icon: Icon(Icons.account_circle), label: '我的'),
                BottomNavigationBarItem(
                    icon: Icon(Icons.handyman), label: '测试'),
              ],
              currentIndex: controller.currentIndex.value,
              onTap: (index) {
                if (index == 1) {
                  LoginController loginController = Get.find();
                  loginController.isLogin.value
                      ? controller.currentIndex.value = 1
                      : Get.toNamed(ConstData.loginPage);
                } else {
                  controller.currentIndex.value = index;
                }
              },
            )),
      ),
      initialRoute: '/',
      getPages: AllRoutes.allRoutes,
      initialBinding: AllBinding(),
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
    ));
  }
}
