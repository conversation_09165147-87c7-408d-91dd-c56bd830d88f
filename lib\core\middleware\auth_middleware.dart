import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../controllers/character_controller.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    CharacterController controller = Get.find();

    // 判断用户是否选择了默认界面，默认在打开一个界面的时候，都会初始化信息控制器。
    // 如果当前用户设置了自定义界面，那么就获取到该角色对应的路由“toPageName”，具体方式是获取到所有角色列表，再获取当前角色id，来取得该角色信息
    // 如果没有设置，那么就自动跳转到默认聊天界面“/Chat”

    return controller.isCurrentCharOnCustomPage.value
        ? RouteSettings(
            name: controller
                .characterSettings[controller.currentCharacterId.value]
                ?.customPageName)
        : const RouteSettings(name: '/Chat');
  }
}
