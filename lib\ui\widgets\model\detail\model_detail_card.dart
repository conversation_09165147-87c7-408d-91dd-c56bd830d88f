// {{ AURA-X: Add - 创建模型详细信息卡片组件. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/page/profile_controller.dart';
import '../../../../ui/widgets/toast/toast.dart';
import '../shared/model_utils.dart';

class ModelDetailCard extends StatelessWidget {
  final dynamic model;
  final ProfileController controller;

  const ModelDetailCard({
    Key? key,
    required this.model,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final modelName = ModelUtils.getModelName(model);
    final modelDescription = ModelUtils.getModelDescription(model);
    final inputPrice = ModelUtils.getInputPrice(model);
    final outputPrice = ModelUtils.getOutputPrice(model);
    final priceTip = ModelUtils.getPriceTip(model);
    final tags = ModelUtils.getTags(model);
    final maxToken = ModelUtils.getMaxToken(model);

    return Obx(() => Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: ModelUtils.isCurrentModel(model, controller.currentModelName.value)
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.outlineVariant,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 模型名称和状态
                _buildHeader(context, modelName),
                
                const SizedBox(height: 20),
                
                // 标签区域
                if (tags.isNotEmpty) ...[
                  _buildSection(
                    context,
                    '标签',
                    Icons.label_outline,
                    ModelUtils.buildTagList(tags, compact: false, spacing: 8),
                  ),
                  const SizedBox(height: 20),
                ],
                
                // 价格信息
                _buildPriceSection(context, inputPrice, outputPrice, priceTip),
                
                const SizedBox(height: 20),
                
                // 技术规格
                _buildSpecSection(context, maxToken),
                
                const SizedBox(height: 20),
                
                // 模型描述
                _buildDescriptionSection(context, modelDescription),
                
                const SizedBox(height: 24),
                
                // 操作按钮
                _buildActionButton(context, modelName),
              ],
            ),
          ),
        ));
  }

  Widget _buildHeader(BuildContext context, String modelName) {
    final isSelected = ModelUtils.isCurrentModel(model, controller.currentModelName.value);

    return Row(
      children: [
        Icon(
          Icons.smart_toy,
          color: Theme.of(context).primaryColor,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            modelName,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        if (isSelected)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Text(
              '当前使用',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSection(BuildContext context, String title, IconData icon, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildPriceSection(BuildContext context, double inputPrice, double outputPrice, String priceTip) {
    return _buildSection(
      context,
      '价格信息',
      Icons.attach_money,
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Column(
          children: [
            if (inputPrice == 0.0 && outputPrice == 0.0)
              Row(
                children: [
                  Icon(Icons.free_breakfast, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    '免费使用',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              )
            else ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('输入价格:', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text('${inputPrice.toStringAsFixed(2)} / 百万tokens'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('输出价格:', style: TextStyle(fontWeight: FontWeight.w500)),
                  Text('${outputPrice.toStringAsFixed(2)} / 百万tokens'),
                ],
              ),
            ],
            if (priceTip.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        priceTip,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSpecSection(BuildContext context, int maxToken) {
    return _buildSection(
      context,
      '技术规格',
      Icons.memory,
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('最大Token数:', style: TextStyle(fontWeight: FontWeight.w500)),
            Text(
              ModelUtils.formatNumber(maxToken),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection(BuildContext context, String description) {
    return _buildSection(
      context,
      '模型描述',
      Icons.description,
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Text(
          description,
          style: const TextStyle(
            fontSize: 14,
            height: 1.6,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, String modelName) {
    final isSelected = ModelUtils.isCurrentModel(model, controller.currentModelName.value);

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: isSelected ? null : () => _selectModel(context, modelName),
        icon: Icon(isSelected ? Icons.check : Icons.touch_app),
        label: Text(isSelected ? '当前使用中' : '使用此模型'),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  void _selectModel(BuildContext context, String modelName) {
    controller.setCurrentModel(model);
    Toast.showSuccess('切换成功', '当前模型：$modelName');
  }
}
