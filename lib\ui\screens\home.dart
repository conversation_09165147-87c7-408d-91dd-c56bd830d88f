import 'package:aichat/core/controllers/character_controller.dart';
import 'package:aichat/data/const.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/controllers/messages_controller.dart';
import '../../data/models/character_setting.dart';

class HomePage extends StatefulWidget {
  @override
  State<HomePage> createState() => _HomePage();
}

class _HomePage extends State<HomePage> {
  final List<AICharacter> characters = ConstData.allCharacters;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      appBar: AppBar(
        title: Text(
          '与你',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              itemCount: characters.length,
              controller: PageController(viewportFraction: 0.9),
              itemBuilder: (context, index) {
                return CharacterCard(character: characters[index]);
              },
            ),
          ),
          const SizedBox(height: 20),
          Text(
            '选择一个AI角色开始聊天',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}

class CharacterCard extends GetView<CharacterController> {
  final AICharacter character;

  const CharacterCard({Key? key, required this.character}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 25),
      child: GestureDetector(
        onTap: () {
          // id 进行全局赋值
          controller.currentCharacterId.value = character.id;
          Get.toNamed(
            '/Chat',
            arguments: Get.put(MessagesController()),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(35),
            border: Border.all(
                color: Theme.of(context).colorScheme.surfaceContainer,
                width: 1.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(35),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  flex: 6,
                  child: Image.asset(
                    character.background,
                    fit: BoxFit.cover,
                  ),
                ),
                Expanded(
                  flex: 5,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              character.name,
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Text(
                              character.description.replaceAll('\n', ' '),
                              // character.description,
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 15,
                                color: Theme.of(context).colorScheme.primary,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
