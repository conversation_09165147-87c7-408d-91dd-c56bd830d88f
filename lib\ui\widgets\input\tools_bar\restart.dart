import 'package:aichat/core/controllers/data_controller.dart';
import 'package:aichat/core/controllers/messages_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Restart extends GetView<MessagesController> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 关闭工具栏
        controller.isPressInputTools.value = false;
        // 显示弹窗
        showCupertinoDialog(
          context: context,
          builder: (BuildContext context) {
            return _buildDialog(context);
          },
        );
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8),
            child: Container(
                height: 45,
                width: 45,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3), // 半透明背景色，增强视觉效果
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                ),
                child: const Icon(IconData(0xe022, fontFamily: 'dripicons'))),
          ),
          const Text('重启')
        ],
      ),
    );
  }

  Widget _buildDialog(BuildContext context) {
    DataController dataController = Get.find();

    return CupertinoAlertDialog(
      title: const Text('重启对话'),
      content: const Text('重启后，该角色将忘记与您的历史对话'),
      actions: [
        CupertinoDialogAction(
          isDestructiveAction: true,
          onPressed: () {
            // 删除 前台显示与本地储存 的对话
            controller.clearMessage();

            Navigator.of(context).pop(); // 关闭弹窗
          },
          child: const Text('确认'),
        ),
        CupertinoDialogAction(
          isDefaultAction: true,
          onPressed: () {
            Navigator.of(context).pop(); // 关闭弹窗
          },
          child: const Text('取消'),
        ),
      ],
    );
  }
}
