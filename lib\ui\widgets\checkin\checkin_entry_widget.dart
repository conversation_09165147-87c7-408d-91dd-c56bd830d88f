// {{ AURA-X: Add - 创建Profile页面签到入口组件. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/controllers/checkin_controller.dart';

class CheckinEntryWidget extends StatelessWidget {
  const CheckinEntryWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 尝试获取已存在的控制器，如果不存在则创建新的
    CheckinController controller;
    try {
      controller = Get.find<CheckinController>();
    } catch (e) {
      controller = Get.put(CheckinController());
    }

    return Obx(() => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              Get.toNamed('/Checkin');
            },
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  // 标题行
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.calendar_today,
                          color: Colors.orange,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '每日签到',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '签到获取额度奖励',
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // 签到状态和统计信息
                  Row(
                    children: [
                      // 今日签到状态
                      Expanded(
                        child: _buildStatusItem(
                          context: context,
                          icon: controller.hasCheckedInToday.value
                              ? Icons.check_circle
                              : Icons.radio_button_unchecked,
                          title: '今日状态',
                          value: controller.hasCheckedInToday.value
                              ? '已签到'
                              : '未签到',
                          color: controller.hasCheckedInToday.value
                              ? Colors.green
                              : Colors.grey,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // 连续签到天数
                      Expanded(
                        child: _buildStatusItem(
                          context: context,
                          icon: Icons.local_fire_department,
                          title: '连续签到',
                          value: '${controller.consecutiveDays.value}天',
                          color: Colors.orange,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // 累计奖励
                      Expanded(
                        child: _buildStatusItem(
                          context: context,
                          icon: Icons.monetization_on,
                          title: '累计奖励',
                          value: controller.totalRewards.value > 0
                              ? '${controller.totalRewards.value.toStringAsFixed(1)}'
                              : '0.0',
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),

                  // 如果今日未签到，显示快速签到按钮
                  if (!controller.hasCheckedInToday.value &&
                      controller.canCheckin.value) ...[
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: controller.isLoading.value
                            ? null
                            : () async {
                                await controller.performCheckin();
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: controller.isLoading.value
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.touch_app, size: 20),
                                  SizedBox(width: 8),
                                  Text(
                                    '快速签到',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ));
  }

  // 构建状态项
  Widget _buildStatusItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}
