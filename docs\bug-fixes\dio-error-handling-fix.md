# Dio 错误处理空值问题修复

## 问题描述

在签到功能中出现了类型转换错误：
```
Error: type 'Null' is not a subtype of type 'String'
```

### 错误日志
```
I/flutter ( 3170): === ERROR DEBUG INFO ===
I/flutter ( 3170): 错误类型: DioExceptionType.badResponse
I/flutter ( 3170): 请求URL: https://api.kyooo.top/checkin/
I/flutter ( 3170): 请求方法: POST
I/flutter ( 3170): 签到请求失败: DioException [unknown]: null
I/flutter ( 3170): Error: type 'Null' is not a subtype of type 'String'
I/flutter ( 3170): 签到失败: Exception: 签到失败: DioException [unknown]: null
```

## 根本原因

问题出现在 `lib/api/http/interceptor.dart` 的 `ErrorInterceptor` 类中：

1. 服务器返回了 `badResponse` 错误（状态码可能是 400/500 等）
2. 在第 124 行，代码尝试直接访问 `e.response?.data['message']` 和 `e.response?.data['details']`
3. 当这些字段为 `null` 时，Toast.showError 方法期望 String 参数，导致类型转换错误

### 原始问题代码
```dart
// 设置弹窗显示报错信息
if (e.response?.data != null && e.response?.data != Null && e.response?.data is Map){
  Toast.showError(e.response?.data['message'], e.response?.data['details']);
}
```

## 解决方案

### 修复内容

1. **增强空值检查**：确保在访问 Map 字段前进行类型检查
2. **提供默认值**：为 message 和 details 提供合理的默认值
3. **完善错误处理**：处理不同类型的响应错误情况

### 修复后的代码
```dart
// {{ AURA-X: Modify - 修复空值处理，避免类型转换错误. Approval: 寸止(ID:2025-08-03T15:30:00+08:00). }}
// 设置弹窗显示报错信息
if (e.response?.data != null && e.response?.data is Map) {
  final responseData = e.response!.data as Map<String, dynamic>;
  final message = responseData['message']?.toString() ?? '请求失败';
  final details = responseData['details']?.toString() ?? '未知错误';
  Toast.showError(message, details);
} else if (e.response != null) {
  // 如果有响应但数据格式不正确，显示状态码信息
  Toast.showError('请求失败', '服务器返回错误 (${e.response!.statusCode})');
} else {
  // 如果没有响应，显示网络错误信息
  Toast.showError('网络错误', '无法连接到服务器，请检查网络连接');
}
```

## 修复要点

1. **类型安全**：使用 `?.toString()` 确保类型转换安全
2. **空值处理**：使用 `??` 操作符提供默认值
3. **分层处理**：针对不同的错误情况提供不同的处理逻辑
4. **用户友好**：提供清晰的错误提示信息

## 影响范围

- **修复文件**：`lib/api/http/interceptor.dart`
- **影响功能**：所有使用 Dio 进行网络请求的功能
- **特别影响**：签到功能、API 密钥管理、模型列表获取等

## 测试建议

1. **签到功能测试**：测试正常签到和重复签到的错误处理
2. **网络错误测试**：断网情况下的错误提示
3. **服务器错误测试**：模拟 4xx/5xx 状态码的错误处理
4. **API 格式错误测试**：模拟服务器返回非标准格式数据的情况

## 预防措施

1. **代码审查**：在处理外部 API 响应时，始终进行空值检查
2. **类型安全**：使用 Dart 的空安全特性，避免直接访问可能为空的字段
3. **错误处理标准化**：建立统一的错误处理模式
4. **测试覆盖**：增加对异常情况的测试覆盖

## 相关文件

- `lib/api/http/interceptor.dart` - 主要修复文件
- `lib/ui/widgets/toast/toast.dart` - Toast 显示组件
- `lib/api/checkin_api_service.dart` - 签到 API 服务
- `lib/core/controllers/checkin_controller.dart` - 签到控制器

## 版本信息

- **修复日期**：2025-08-03
- **修复版本**：当前开发版本
- **修复人员**：AI Assistant (AURA-X 协议)
