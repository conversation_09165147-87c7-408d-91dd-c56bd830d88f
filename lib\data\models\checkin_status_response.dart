// {{ AURA-X: Add - 创建签到状态响应数据模型类. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
class CheckinStatusResponse {
  final bool hasCheckedInToday;
  final bool canCheckin;

  CheckinStatusResponse({
    required this.hasCheckedInToday,
    required this.canCheckin,
  });

  factory CheckinStatusResponse.fromJson(Map<String, dynamic> json) {
    return CheckinStatusResponse(
      hasCheckedInToday: json['has_checked_in_today'] ?? false,
      canCheckin: json['can_checkin'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'has_checked_in_today': hasCheckedInToday,
      'can_checkin': canCheckin,
    };
  }

  // 获取签到按钮显示文本
  String get checkinButtonText {
    if (hasCheckedInToday) {
      return '已签到';
    } else if (canCheckin) {
      return '立即签到';
    } else {
      return '暂不可签到';
    }
  }

  // 获取签到状态描述
  String get statusDescription {
    if (hasCheckedInToday) {
      return '今日已完成签到';
    } else if (canCheckin) {
      return '今日尚未签到';
    } else {
      return '签到功能暂时不可用';
    }
  }
}
