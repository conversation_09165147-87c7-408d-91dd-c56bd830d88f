// {{ AURA-X: Add - 创建模型相关共享工具方法. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import '../../../../data/models/ai_model.dart';

class ModelUtils {
  /// 格式化价格显示文本
  static String formatPriceText(double inputPrice, double outputPrice, {bool simplified = false}) {
    if (inputPrice == 0.0 && outputPrice == 0.0) {
      return '免费';
    }
    
    if (simplified) {
      // 简化显示：只显示输入价格
      return '${inputPrice.toStringAsFixed(2)}/M';
    } else {
      // 完整显示：输入/输出价格
      return '输入: ${inputPrice.toStringAsFixed(2)}/M • 输出: ${outputPrice.toStringAsFixed(2)}/M';
    }
  }

  /// 获取主要标签（用于简化显示）
  static List<String> getMainTags(List<String> tags, {int maxCount = 2}) {
    // 优先显示重要标签
    const priorityTags = ['特价', '推荐', '高性能'];
    
    List<String> mainTags = [];
    
    // 先添加优先级标签
    for (String priorityTag in priorityTags) {
      if (tags.contains(priorityTag) && mainTags.length < maxCount) {
        mainTags.add(priorityTag);
      }
    }
    
    // 如果还有空间，添加其他标签
    for (String tag in tags) {
      if (!mainTags.contains(tag) && mainTags.length < maxCount) {
        mainTags.add(tag);
      }
    }
    
    return mainTags;
  }

  /// 构建标签Widget
  static Widget buildTag(String tag, {double fontSize = 10, bool compact = true}) {
    final tagStyle = AIModel.getTagStyle(tag);
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 6 : 12,
        vertical: compact ? 2 : 6,
      ),
      decoration: BoxDecoration(
        color: Color(tagStyle['color']).withOpacity(0.1),
        borderRadius: BorderRadius.circular(compact ? 6 : 12),
        border: Border.all(
          color: Color(tagStyle['color']).withOpacity(0.3),
          width: compact ? 0.5 : 1,
        ),
      ),
      child: Text(
        tagStyle['text'],
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w500,
          color: Color(tagStyle['color']),
        ),
      ),
    );
  }

  /// 构建标签列表Widget
  static Widget buildTagList(List<String> tags, {
    bool compact = true,
    int? maxTags,
    double spacing = 4,
  }) {
    final displayTags = maxTags != null ? tags.take(maxTags).toList() : tags;
    
    return Wrap(
      spacing: spacing,
      runSpacing: spacing,
      children: displayTags.map((tag) => buildTag(tag, compact: compact)).toList(),
    );
  }

  /// 格式化Token数显示
  static String formatTokenCount(int tokenCount) {
    if (tokenCount >= 1000000) {
      double millions = tokenCount / 1000000;
      return '${millions.toStringAsFixed(millions == millions.roundToDouble() ? 0 : 1)}M';
    } else if (tokenCount >= 1000) {
      double thousands = tokenCount / 1000;
      return '${thousands.toStringAsFixed(thousands == thousands.roundToDouble() ? 0 : 1)}K';
    } else {
      return tokenCount.toString();
    }
  }

  /// 格式化数字显示（添加千分位分隔符）
  static String formatNumber(int number) {
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  /// 获取模型数据的安全访问方法
  static String getModelName(dynamic model) {
    return model['name'] ?? '未知模型';
  }

  static String getModelDescription(dynamic model) {
    return model['descriptions'] ?? '暂无描述';
  }

  static double getInputPrice(dynamic model) {
    return (model['input_price_per_mtokens'] ?? 0.0).toDouble();
  }

  static double getOutputPrice(dynamic model) {
    return (model['output_price_per_mtokens'] ?? 0.0).toDouble();
  }

  static String getPriceTip(dynamic model) {
    return model['price_tip'] ?? '';
  }

  static List<String> getTags(dynamic model) {
    return List<String>.from(model['tags'] ?? []);
  }

  static int getMaxToken(dynamic model) {
    return model['maxToken'] ?? 0;
  }

  /// 检查是否为当前选中的模型
  static bool isCurrentModel(dynamic model, String currentModelName) {
    return getModelName(model) == currentModelName;
  }

  /// 构建价格信息容器
  static Widget buildPriceContainer(double inputPrice, double outputPrice, {bool simplified = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.amber.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.attach_money,
            size: 12,
            color: Colors.amber.shade800,
          ),
          const SizedBox(width: 2),
          Text(
            formatPriceText(inputPrice, outputPrice, simplified: simplified),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.amber.shade800,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建免费标签
  static Widget buildFreeTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        "免费使用",
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.green[600],
        ),
      ),
    );
  }
}
