import 'package:aichat/data/const.dart';
import 'package:aichat/ui/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/controllers/page/profile_controller.dart';
import '../../theme/light_dark/theme.dart';
import '../../widgets/checkin/checkin_entry_widget.dart';
import '../../widgets/model/selection/model_selection_section.dart';

class ProfilePage extends StatefulWidget {
  @override
  State<ProfilePage> createState() => ProfilePageState();
}

class ProfilePageState extends State<ProfilePage> {
  final ProfileController controller = Get.find();

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () async {
      await controller.refreshKeyInfo(); // 每次打开页面刷新密钥/额度信息
      await controller.loadModels(); // 每次打开页面刷新模型信息
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('个人中心'),
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          Get.isDarkMode
              ? IconButton(
                  icon: const Icon(Icons.light_mode),
                  onPressed: () {
                    Get.changeTheme(AppTheme.light);
                  },
                )
              : IconButton(
                  icon: const Icon(Icons.nightlight),
                  onPressed: () {
                    Get.changeTheme(AppTheme.dark);
                  },
                ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Get.toNamed(ConstData.settingPage);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // {{ AURA-X: Modify - 使用新的模型选择组件替换原有实现. Approval: 寸止(ID:2025-08-04). }}
              // 模型选择区域
              ModelSelectionSection(controller: controller),

              const SizedBox(height: 24),

              // 账号信息区域
              _buildSectionTitle('账号信息'),
              const SizedBox(height: 16),
              _buildAccountInfoSection(),

              const SizedBox(height: 24),

              // {{ AURA-X: Add - 添加签到功能入口. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
              // 签到功能区域
              _buildSectionTitle('每日签到'),
              const SizedBox(height: 16),
              const CheckinEntryWidget(),

              const SizedBox(height: 24),

              // 额度信息区域
              _buildSectionTitle('额度信息'),
              const SizedBox(height: 16),
              _buildQuotaInfoSection(),

              const SizedBox(height: 24),

              // 付费入口
              _buildPaymentSection(context),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  // 构建区域标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }










  // 账号信息区域
  Widget _buildAccountInfoSection() {
    return Obx(() => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(16),
            // border: Border.all(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                _buildInfoRow(
                  icon: Icons.person,
                  title: '账号 ID',
                  value: controller.accountId.value,
                  canCopy: true,
                ),
                const Divider(height: 24),
                _buildKeyInfoRow(),
              ],
            ),
          ),
        ));
  }

  // 信息行组件
  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String value,
    bool canCopy = false,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 22,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
        ),
        if (canCopy)
          IconButton(
            icon: const Icon(Icons.copy, size: 20),
            onPressed: () async {
              // 复制到剪贴板
              await Clipboard.setData(ClipboardData(text: value));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已复制$title')),
              );
            },
          ),
      ],
    );
  }

  // 密钥信息行组件（带显示/隐藏功能）
  Widget _buildKeyInfoRow() {
    return Obx(() => Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.key,
                color: Theme.of(context).primaryColor,
                size: 22,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '密钥',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // 使用可选择的文本组件，支持长按选择
                  SelectableText(
                    controller.displayKeyValue,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                      fontFamily: 'monospace', // 使用等宽字体，让密钥显示更整齐
                    ),
                    maxLines: 2, // 允许最多2行显示
                  ),
                ],
              ),
            ),
            // 显示/隐藏按钮
            IconButton(
              icon: Icon(
                controller.isKeyVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
                size: 20,
              ),
              onPressed: controller.toggleKeyVisibility,
              tooltip: controller.isKeyVisible.value ? '隐藏密钥' : '显示密钥',
            ),
            // 复制按钮
            IconButton(
              icon: const Icon(Icons.copy, size: 20),
              onPressed: () async {
                // 复制完整的密钥值到剪贴板
                await Clipboard.setData(
                    ClipboardData(text: controller.keyValue.value));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('已复制密钥')),
                );
              },
              tooltip: '复制密钥',
            ),
          ],
        ));
  }

  // 额度信息区域
  Widget _buildQuotaInfoSection() {
    return Obx(() {
      final int total = controller.totalQuota.value;
      final int used = controller.usedQuota.value;
      final int remaining = controller.remainingQuota.value;

      // 防止除零错误
      double usedPercentage = 0.0;
      if (total > 0) {
        usedPercentage = used / total;
        // 确保百分比在0-1之间
        usedPercentage = usedPercentage.clamp(0.0, 1.0);
      }

      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(16),
          // border: Border.all(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 进度条
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '已使用 ${(usedPercentage * 100).toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _getQuotaColor(usedPercentage),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: usedPercentage,
                      backgroundColor:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      valueColor: AlwaysStoppedAnimation<Color>(
                          _getQuotaColor(usedPercentage)),
                      minHeight: 8,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // 额度详情
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuotaDetailItem(
                    icon: Icons.data_usage,
                    title: '总额度',
                    value: _formatNumber(total),
                  ),
                  _buildQuotaDetailItem(
                    icon: Icons.check_circle_outline,
                    title: '剩余额度',
                    value: _formatNumber(remaining),
                    valueColor: Colors.green,
                  ),
                  _buildQuotaDetailItem(
                    icon: Icons.history,
                    title: '已用额度',
                    value: _formatNumber(used),
                    valueColor: _getQuotaColor(usedPercentage),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  // 额度详情项
  Widget _buildQuotaDetailItem({
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Column(
      children: [
        Icon(icon,
            color:
                valueColor ?? Theme.of(context).colorScheme.onSurfaceVariant),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: valueColor ?? Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // 获取额度对应的颜色
  Color _getQuotaColor(double percentage) {
    if (percentage > 0.8) {
      return Colors.red;
    } else if (percentage > 0.6) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  // 格式化数字
  // 把额度转换为特定单位后再显示出来
  String _formatNumber(int number) {
    return "${((number / 500000) < 0 ? 0 : (number / 500000)).toStringAsFixed(2)}￥";
  }

  // 付费入口
  Widget _buildPaymentSection(context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.payments_outlined,
                    color: Theme.of(context).primaryColor,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '额度充值',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 6),
                      Text(
                        '购买额度以继续使用AI聊天服务',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.credit_card),
                    label: const Text('卡密充值'),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () {
                      _showRechargeDialog(context);
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('购买卡密'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () {
                      controller.openPaymentWebsite();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 显示充值对话框
  void _showRechargeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('卡密充值'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('请输入您的卡密:'),
              const SizedBox(height: 16),
              TextField(
                controller: controller.cardCodeController,
                decoration: const InputDecoration(
                  hintText: '请输入卡密',
                  // border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => controller.message.value.isNotEmpty
                  ? Flexible(
                      child: Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: controller.message.value.contains('成功')
                              ? Colors.green.withOpacity(0.1)
                              : Theme.of(context)
                                  .colorScheme
                                  .error
                                  .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: controller.message.value.contains('成功')
                                ? Colors.green.withOpacity(0.3)
                                : Theme.of(context)
                                    .colorScheme
                                    .error
                                    .withOpacity(0.3),
                          ),
                        ),
                        constraints: const BoxConstraints(
                          maxHeight: 120, // 限制最大高度
                        ),
                        child: SingleChildScrollView(
                          child: Text(
                            controller.message.value,
                            style: TextStyle(
                              color: controller.message.value.contains('成功')
                                  ? Colors.green
                                  : Theme.of(context).colorScheme.error,
                              fontSize: 14,
                              height: 1.4, // 增加行高提高可读性
                            ),
                          ),
                        ),
                      ),
                    )
                  : const SizedBox()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              controller.message.value = '';
            },
            child: const Text('取消'),
          ),
          Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () async {
                        await controller.addKeyQuota();
                        if (controller.message.value.contains('成功')) {
                          Future.delayed(const Duration(seconds: 1), () {
                            Navigator.pop(context);
                            controller.message.value = '';
                          });
                        }
                      },
                child: controller.isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('确认充值'),
              )),
        ],
      ),
    );
  }

}
