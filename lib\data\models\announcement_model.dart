// {{ AURA-X: Add - 创建公告数据模型类. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
class AnnouncementModel {
  final int id;
  final String title;
  final String content;
  final String type; // info, warning, urgent
  final int priority;
  final bool isActive;
  final String createdAt;
  final String? expiresAt;

  AnnouncementModel({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.isActive,
    required this.createdAt,
    this.expiresAt,
  });

  factory AnnouncementModel.fromJson(Map<String, dynamic> json) {
    return AnnouncementModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      type: json['type'] ?? 'info',
      priority: json['priority'] ?? 0,
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] ?? '',
      expiresAt: json['expires_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type,
      'priority': priority,
      'is_active': isActive,
      'created_at': createdAt,
      'expires_at': expiresAt,
    };
  }

  // 获取公告类型对应的图标
  String get typeIcon {
    switch (type) {
      case 'warning':
        return '⚠️';
      case 'urgent':
        return '🚨';
      case 'info':
      default:
        return 'ℹ️';
    }
  }

  // 获取公告类型的显示名称
  String get typeDisplayName {
    switch (type) {
      case 'warning':
        return '警告';
      case 'urgent':
        return '紧急';
      case 'info':
      default:
        return '信息';
    }
  }

  // {{ AURA-X: Add - 添加时间格式化方法. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
  // 获取格式化的发布时间
  String get formattedCreatedAt {
    try {
      final dateTime = DateTime.parse(createdAt);
      return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return '时间未知';
    }
  }

  // 获取用于排序的DateTime对象
  DateTime get createdDateTime {
    try {
      return DateTime.parse(createdAt);
    } catch (e) {
      return DateTime.fromMillisecondsSinceEpoch(0); // 返回最早时间作为默认值
    }
  }
}
