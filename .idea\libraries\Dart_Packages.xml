<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="_fe_analyzer_shared">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-47.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="analyzer">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/analyzer-4.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="asn1lib">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/asn1lib-1.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.12.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="base64">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/base64-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="bubble">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/bubble-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_config">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_config-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_daemon">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_resolvers">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_resolvers-2.0.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner-2.4.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="build_runner_core">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner_core-7.2.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_collection">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="built_value">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_value-8.9.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="code_builder">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/code_builder-4.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="common_utils">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/common_utils-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/convert-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dart_style">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_style-2.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="decimal">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/decimal-2.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio-5.8.0+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="dio_web_adapter">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="encrypt">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/encrypt-5.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="fernet">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fernet-0.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="fetch_api">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fetch_api-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="fetch_client">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fetch_client-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="fixnum">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flex_color_scheme">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flex_color_scheme-8.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flex_seed_scheme">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flex_seed_scheme-3.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../APP/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_im_list">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/git/flutter_im_list-2c7f5ae166bfc2853b429e131e864b10b339eda1//lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_lints-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_markdown">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_markdown-0.7.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_svg-2.0.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../APP/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../APP/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/freezed_annotation-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="frontend_server_client">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="get">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="glob">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_fonts">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/google_fonts-6.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="graphs">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/graphs-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hive-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hive_flutter-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="hive_generator">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hive_generator-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_multi_server">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="iconsax_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/iconsax_flutter-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="iconsax_plus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/iconsax_plus-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/intl-0.18.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="io">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/io-1.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/js-0.7.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="langchain">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain-0.7.7+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="langchain_core">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain_core-0.3.6+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="langchain_openai">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain_openai-0.7.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="langchain_tiktoken">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain_tiktoken-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/lints-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="markdown">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/markdown-7.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="openai_dart">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/openai_dart-0.4.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_config-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pausable_timer">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pausable_timer-3.1.0+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/petitparser-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="pointycastle">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pointycastle-3.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="pool">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pubspec_parse">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pull_down_button">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pull_down_button-0.10.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="rational">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/rational-2.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/rxdart-0.28.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf-1.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_web_socket-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../APP/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_gen">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_gen-1.2.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_helper">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_helper-1.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sprintf">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_transform">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="tiktoken_tokenizer_gpt4o_o1">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/tiktoken_tokenizer_gpt4o_o1-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="timing">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timing-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="toastification">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/toastification-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="toggle_switch">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/toggle_switch-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.15/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="uuid">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/uuid-4.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics-1.1.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics_codec-1.1.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics_compiler-1.1.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-14.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="watcher">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/watcher-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket-0.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/win32-5.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml-3.1.3/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/git/flutter_im_list-2c7f5ae166bfc2853b429e131e864b10b339eda1//lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/_fe_analyzer_shared-47.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/analyzer-4.7.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/asn1lib-1.6.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/async-2.12.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/base64-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/bubble-1.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build-2.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_config-1.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_daemon-4.0.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_resolvers-2.0.10/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner-2.4.13/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/build_runner_core-7.2.10/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_collection-5.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/built_value-8.9.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/checked_yaml-2.0.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/code_builder-4.10.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/common_utils-2.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/convert-3.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dart_style-2.2.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/decimal-2.3.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio-5.8.0+1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/dio_web_adapter-2.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/encrypt-5.0.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/equatable-2.0.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fake_async-1.3.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fernet-0.0.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fetch_api-2.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fetch_client-1.1.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fixnum-1.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flex_color_scheme-8.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flex_seed_scheme-3.5.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_lints-3.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_markdown-0.7.7/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/flutter_svg-2.0.17/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/freezed_annotation-2.4.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/frontend_server_client-4.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/get-4.7.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/glob-2.1.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/google_fonts-6.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/graphs-2.3.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hive-2.2.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hive_flutter-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/hive_generator-1.1.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http-1.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_multi_server-3.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/iconsax_flutter-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/iconsax_plus-1.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/intl-0.18.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/io-1.0.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/js-0.7.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain-0.7.7+2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain_core-0.3.6+1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain_openai-0.7.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/langchain_tiktoken-1.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker-10.0.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/lints-3.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/markdown-7.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/openai_dart-0.4.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_config-2.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-3.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.16/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pausable_timer-3.1.0+3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pointycastle-3.9.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pool-1.5.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pub_semver-2.2.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pubspec_parse-1.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/pull_down_button-0.10.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/rational-2.2.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/rxdart-0.28.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf-1.4.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/shelf_web_socket-2.0.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_gen-1.2.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_helper-1.3.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/sprintf-7.0.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/stream_transform-2.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/tiktoken_tokenizer_gpt4o_o1-1.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/timing-1.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/toastification-3.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/toggle_switch-2.3.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_android-6.3.15/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_web-2.4.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/uuid-4.5.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics-1.1.18/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics_codec-1.1.13/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_graphics_compiler-1.1.16/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/vm_service-14.3.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/watcher-1.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket-0.1.6/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/web_socket_channel-3.0.2/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/win32-5.13.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/yaml-3.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../APP/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$PROJECT_DIR$/../../../APP/flutter/packages/flutter/lib" />
      <root url="file://$PROJECT_DIR$/../../../APP/flutter/packages/flutter_test/lib" />
      <root url="file://$PROJECT_DIR$/../../../APP/flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>