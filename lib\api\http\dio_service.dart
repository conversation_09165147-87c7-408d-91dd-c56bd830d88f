// 处理有关dio部分的内容

import 'package:dio/dio.dart';

import 'interceptor.dart';

class DioService {
  static const String _baseUrl = 'https://api.kyooo.top';
  static final Dio _dio = Dio(
    BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
    ),
  );

  static Dio get instance {
    if (_dio.interceptors.length == 1) {
      _dio.interceptors.addAll([
        OnRequestInterceptor(),
        OnResponseInterceptor(),
        ErrorInterceptor(),
      ]);
    }
    return _dio;
  }
}
