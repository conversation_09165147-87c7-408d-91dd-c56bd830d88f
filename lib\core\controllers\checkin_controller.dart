// {{ AURA-X: Add - 创建签到功能控制器. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../api/checkin_api_service.dart';
import '../../data/const.dart';
import '../../data/models/checkin_model.dart';
import '../../data/models/checkin_history_response.dart';
import '../../data/models/checkin_status_response.dart';
import '../../ui/widgets/toast/toast.dart';

class CheckinController extends GetxController {
  final CheckinApiService _apiService = CheckinApiService();

  // 签到状态
  final RxBool isLoading = false.obs;
  final RxBool hasCheckedInToday = false.obs;
  final RxBool canCheckin = true.obs;

  // 签到历史数据
  final RxList<CheckinModel> checkinHistory = <CheckinModel>[].obs;
  final RxInt totalCheckinDays = 0.obs;
  final RxDouble totalRewards = 0.0.obs;
  final RxInt consecutiveDays = 0.obs;

  // 当前选中的日期（用于日历显示详情）
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final Rx<CheckinModel?> selectedDateCheckin = Rx<CheckinModel?>(null);

  // 当前月份（用于日历导航）
  final Rx<DateTime> currentMonth = DateTime.now().obs;

  @override
  void onInit() {
    super.onInit();
    _initData();
  }

  // 初始化数据
  Future<void> _initData() async {
    await loadCheckinData();
  }

  // 加载签到数据
  Future<void> loadCheckinData() async {
    try {
      isLoading.value = true;

      // 并行获取签到状态和历史记录
      final futures = await Future.wait([
        _apiService.getCheckinStatus(),
        _apiService.getCheckinHistory(limit: 100), // 获取更多历史记录用于日历显示
      ]);

      final status = futures[0] as CheckinStatusResponse;
      final history = futures[1] as CheckinHistoryResponse;

      // 更新状态
      hasCheckedInToday.value = status.hasCheckedInToday;
      canCheckin.value = status.canCheckin;

      // 更新历史数据
      checkinHistory.assignAll(history.history);
      totalCheckinDays.value = history.totalCheckinDays;
      totalRewards.value = history.totalRewards;
      consecutiveDays.value = history.consecutiveDays;

      // 更新选中日期的签到信息
      _updateSelectedDateCheckin();

      // 缓存数据到本地
      await _cacheCheckinData(history);

      print('签到数据加载成功');
    } catch (e) {
      print('加载签到数据失败: $e');
      // 尝试从本地缓存加载数据
      await _loadCachedData();

      // 显示错误提示
      Toast.showError('加载失败', '无法获取签到数据，已显示缓存数据');
    } finally {
      isLoading.value = false;
    }
  }

  // 执行签到
  Future<void> performCheckin() async {
    if (!canCheckin.value || hasCheckedInToday.value) {
      Toast.showError('无法签到', '今日已完成签到或签到功能不可用');
      return;
    }

    try {
      isLoading.value = true;
      Toast.showInfo('签到中', '正在处理签到请求...');

      // 执行签到
      CheckinModel result = await _apiService.dailyCheckin();

      // 更新状态
      hasCheckedInToday.value = true;
      canCheckin.value = false;

      // 添加到历史记录
      checkinHistory.insert(0, result);
      totalCheckinDays.value += 1;
      totalRewards.value += result.rewardAmount;

      // 重新计算连续签到天数
      _updateConsecutiveDays();

      // 更新选中日期的签到信息
      _updateSelectedDateCheckin();

      // 缓存更新后的数据
      await _cacheCheckinData(CheckinHistoryResponse(
        history: checkinHistory,
        totalCheckinDays: totalCheckinDays.value,
        totalRewards: totalRewards.value,
      ));

      // 显示成功提示
      Toast.showSuccess('签到成功！', '获得 ${result.formattedRewardAmount}');
    } catch (e) {
      print('签到失败: $e');
      Toast.showError('签到失败', e.toString().replaceAll('Exception: ', ''));
    } finally {
      isLoading.value = false;
    }
  }

  // 选择日期
  void selectDate(DateTime date) {
    selectedDate.value = date;
    _updateSelectedDateCheckin();
  }

  // 切换月份
  void changeMonth(int monthOffset) {
    currentMonth.value = DateTime(
      currentMonth.value.year,
      currentMonth.value.month + monthOffset,
      1,
    );
  }

  // 更新选中日期的签到信息
  void _updateSelectedDateCheckin() {
    final dateString = _formatDateString(selectedDate.value);
    try {
      selectedDateCheckin.value = checkinHistory.firstWhere(
        (checkin) => checkin.checkinDate == dateString,
      );
    } catch (e) {
      selectedDateCheckin.value = null;
    }
  }

  // 更新连续签到天数
  void _updateConsecutiveDays() {
    if (checkinHistory.isEmpty) {
      consecutiveDays.value = 0;
      return;
    }

    // 按日期排序（最新的在前）
    final sortedHistory = List<CheckinModel>.from(checkinHistory);
    sortedHistory.sort((a, b) => b.checkinDate.compareTo(a.checkinDate));

    int consecutive = 0;
    DateTime? lastDate;

    for (var checkin in sortedHistory) {
      try {
        final currentDate = DateTime.parse(checkin.checkinDate);

        if (lastDate == null) {
          consecutive = 1;
          lastDate = currentDate;
        } else {
          final difference = lastDate.difference(currentDate).inDays;
          if (difference == 1) {
            consecutive++;
            lastDate = currentDate;
          } else {
            break;
          }
        }
      } catch (e) {
        break;
      }
    }

    consecutiveDays.value = consecutive;
  }

  // 缓存签到数据到本地
  Future<void> _cacheCheckinData(CheckinHistoryResponse data) async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      await settingBox.put('checkin_cache', {
        'history': data.history.map((item) => item.toJson()).toList(),
        'total_checkin_days': data.totalCheckinDays,
        'total_rewards': data.totalRewards,
        'cache_time': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('缓存签到数据失败: $e');
    }
  }

  // 从本地缓存加载数据
  Future<void> _loadCachedData() async {
    try {
      await Hive.openBox(ConstData.settingBox);
      Box settingBox = Hive.box(ConstData.settingBox);

      final cachedData = await settingBox.get('checkin_cache');
      if (cachedData != null) {
        final historyList = cachedData['history'] as List? ?? [];
        final history =
            historyList.map((item) => CheckinModel.fromJson(item)).toList();

        checkinHistory.assignAll(history);
        totalCheckinDays.value = cachedData['total_checkin_days'] ?? 0;
        totalRewards.value = (cachedData['total_rewards'] ?? 0.0).toDouble();

        _updateConsecutiveDays();
        _updateSelectedDateCheckin();

        print('从缓存加载签到数据成功');
      }
    } catch (e) {
      print('加载缓存数据失败: $e');
    }
  }

  // 格式化日期字符串
  String _formatDateString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // 检查指定日期是否已签到
  bool isDateCheckedIn(DateTime date) {
    final dateString = _formatDateString(date);
    return checkinHistory.any((checkin) => checkin.checkinDate == dateString);
  }

  // 获取指定日期的签到记录
  CheckinModel? getCheckinForDate(DateTime date) {
    final dateString = _formatDateString(date);
    try {
      return checkinHistory
          .firstWhere((checkin) => checkin.checkinDate == dateString);
    } catch (e) {
      return null;
    }
  }

  // 刷新数据
  Future<void> refreshData() async {
    await loadCheckinData();
  }
}
