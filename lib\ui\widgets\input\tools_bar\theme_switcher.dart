import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/controllers/character_controller.dart';
import '../../../../data/const.dart';
import '../../../../data/models/character_setting.dart';
import '../../../theme/chat_theme.dart';

/// 角色专属主题切换组件
/// 仅在有专属主题的角色聊天界面中显示
class ThemeSwitcher extends GetView<CharacterController> {
  const ThemeSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取当前角色信息
    final AICharacter? character =
        controller.characterSettings[controller.currentCharacterId.value];

    // 只有配置了专属主题的角色才显示此组件
    if (character?.exclusiveTheme == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () => _showThemeDialog(context, character!),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8),
            child: Container(
              height: 45,
              width: 45,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: const BorderRadius.all(Radius.circular(8)),
              ),
              child: Icon(
                Icons.palette_outlined,
                color: Get.theme.colorScheme.onSurface,
                size: 24,
              ),
            ),
          ),
          Text(
            '主题',
            style: TextStyle(
              color: Get.theme.colorScheme.onSurface,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示主题切换对话框
  void _showThemeDialog(BuildContext context, character) async {
    // 检查当前是否使用了角色专属主题
    bool hasCharacterTheme =
        await ChatThemeManager.hasCharacterTheme(character.id);

    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('主题设置'),
          content: Text(
            hasCharacterTheme
                ? '当前使用${character.name}的专属主题，是否切换为全局主题？'
                : '是否启用${character.name}的专属主题？',
          ),
          actions: [
            CupertinoDialogAction(
              onPressed: () async {
                Navigator.of(context).pop();
                await _toggleTheme(context, character, hasCharacterTheme);
              },
              child: Text(hasCharacterTheme ? '使用全局主题' : '启用专属主题'),
            ),
            CupertinoDialogAction(
              isDefaultAction: true,
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 切换主题设置
  Future<void> _toggleTheme(BuildContext context, AICharacter character,
      bool hasCharacterTheme) async {
    try {
      if (hasCharacterTheme) {
        // 当前使用专属主题，切换为全局主题
        await ChatThemeManager.clearCharacterTheme(character.id);
        _showSuccessDialog(context, '已切换为全局主题');
      } else {
        // 当前使用全局主题，启用专属主题
        await ChatThemeManager.saveCharacterTheme(
            character.exclusiveTheme, character.id);
        _showSuccessDialog(context, '已启用${character.name}的专属主题');
      }
    } catch (e) {
      _showErrorDialog(context, '主题切换失败：$e');
    }
  }

  /// 显示成功提示对话框
  void _showSuccessDialog(BuildContext context, String message) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('设置成功'),
          content: Text('$message\n\n请重新打开聊天界面以查看效果。'),
          actions: [
            CupertinoDialogAction(
              isDefaultAction: true,
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 显示错误提示对话框
  void _showErrorDialog(BuildContext context, String message) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('操作失败'),
          content: Text(message),
          actions: [
            CupertinoDialogAction(
              isDefaultAction: true,
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
