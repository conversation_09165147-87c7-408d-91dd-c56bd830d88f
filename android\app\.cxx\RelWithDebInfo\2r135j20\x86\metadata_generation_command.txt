                        -HD:\APP\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\APP\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\APP\Android\Sdk\ndk\27.0.12077973
-<PERSON><PERSON>KE_TOOLCHAIN_FILE=D:\APP\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\APP\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Code\GitHub\aichat\build\app\intermediates\cxx\RelWithDebInfo\2r135j20\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Code\GitHub\aichat\build\app\intermediates\cxx\RelWithDebInfo\2r135j20\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\Code\GitHub\aichat\android\app\.cxx\RelWithDebInfo\2r135j20\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2