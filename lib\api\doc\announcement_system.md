# 公告系统接口文档

## 📋 概述

本文档记录了为 AIChat_Back 项目新增的公告管理系统。该系统采用基于JSON文件的轻量级存储方案，便于快速更新和维护，特别适合小型项目的需求。

## 🎯 设计理念

- **轻量级存储**：使用JSON文件存储公告数据，无需数据库操作
- **便于维护**：直接编辑JSON文件即可更新公告内容
- **统一响应格式**：遵循项目现有的API响应标准
- **权限控制**：所有接口都需要有效的Authorization token

## 📁 文件结构

```
├── config/
│   └── announcements.json        # 公告数据配置文件
├── servier/
│   └── announcement_service.py   # 公告服务API接口
├── docs/
│   └── announcement_system.md    # 本文档
└── main.py                       # 已更新，注册公告路由
```

## 🔧 核心功能

### 1. JSON配置文件 (`config/announcements.json`)

```json
{
  "announcements": [
    {
      "id": 1,
      "title": "公告标题",
      "content": "公告内容",
      "type": "info|warning|urgent",
      "priority": 0,
      "is_active": true,
      "created_at": "2025-08-01T10:00:00Z",
      "expires_at": null
    }
  ],
  "config": {
    "max_display": 10,
    "auto_hide_expired": true,
    "default_type": "info",
    "supported_types": ["info", "warning", "urgent"]
  }
}
```

### 2. 公告字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `id` | int | ✅ | 公告唯一标识符 |
| `title` | string | ✅ | 公告标题 |
| `content` | string | ✅ | 公告内容 |
| `type` | string | ✅ | 公告类型：info(信息)/warning(警告)/urgent(紧急) |
| `priority` | int | ❌ | 优先级，数字越大越靠前显示，默认0 |
| `is_active` | boolean | ❌ | 是否启用，默认true |
| `created_at` | string | ✅ | 创建时间，ISO格式 |
| `expires_at` | string | ❌ | 过期时间，null表示永不过期 |

## 🚀 API接口

### 基础路径
所有公告接口的基础路径为：`/announcements`

### 1. 获取公告列表

```http
GET /announcements
```

**请求头**
```
Authorization: Bearer <token>
```

**查询参数**
- `type` (可选): 公告类型筛选 (info/warning/urgent)
- `limit` (可选): 限制返回数量 (1-50)
- `include_expired` (可选): 是否包含过期公告，默认false

**响应示例**
```json
{
  "success": true,
  "code": 200,
  "message": "获取公告列表成功",
  "data": {
    "announcements": [
      {
        "id": 1,
        "title": "欢迎使用AIChat系统",
        "content": "感谢您使用AIChat智能对话系统！",
        "type": "info",
        "priority": 0,
        "is_active": true,
        "created_at": "2025-08-01T10:00:00Z",
        "expires_at": null
      }
    ],
    "total": 1,
    "config": {
      "supported_types": ["info", "warning", "urgent"],
      "max_display": 10
    }
  },
  "timestamp": "2025-08-01T15:30:00Z"
}
```

### 2. 获取活跃公告

```http
GET /announcements/active
```

**请求头**
```
Authorization: Bearer <token>
```

**查询参数**
- `type` (可选): 公告类型筛选
- `limit` (可选): 限制返回数量

**说明**: 只返回启用且未过期的公告

### 3. 获取单个公告详情

```http
GET /announcements/{announcement_id}
```

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `announcement_id`: 公告ID

**响应示例**
```json
{
  "success": true,
  "code": 200,
  "message": "获取公告详情成功",
  "data": {
    "announcement": {
      "id": 1,
      "title": "欢迎使用AIChat系统",
      "content": "感谢您使用AIChat智能对话系统！",
      "type": "info",
      "priority": 0,
      "is_active": true,
      "created_at": "2025-08-01T10:00:00Z",
      "expires_at": null,
      "status": {
        "is_expired": false,
        "is_active": true,
        "is_available": true
      }
    }
  },
  "timestamp": "2025-08-01T15:30:00Z"
}
```

## 📊 使用示例

### 获取所有公告
```bash
curl -H "Authorization: Bearer <your-token>" \
     http://localhost:8700/announcements
```

### 获取警告类型的公告
```bash
curl -H "Authorization: Bearer <your-token>" \
     "http://localhost:8700/announcements?type=warning&limit=5"
```

### 获取活跃公告
```bash
curl -H "Authorization: Bearer <your-token>" \
     http://localhost:8700/announcements/active
```

### 获取特定公告详情
```bash
curl -H "Authorization: Bearer <your-token>" \
     http://localhost:8700/announcements/1
```

## 🔧 管理公告

### 添加新公告
直接编辑 `config/announcements.json` 文件，在 `announcements` 数组中添加新的公告对象：

```json
{
  "id": 4,
  "title": "新功能发布",
  "content": "我们发布了新的功能，请查看更新日志。",
  "type": "info",
  "priority": 1,
  "is_active": true,
  "created_at": "2025-08-01T16:00:00Z",
  "expires_at": "2025-08-31T23:59:59Z"
}
```

### 修改公告
直接在JSON文件中修改对应公告的字段值。

### 禁用公告
将公告的 `is_active` 字段设置为 `false`。

### 设置过期时间
设置 `expires_at` 字段为具体的过期时间，格式为ISO 8601。

## ⚡ 特性说明

### 1. 自动过期处理
- 系统会自动检查公告的过期时间
- 过期的公告默认不会在列表中显示
- 可通过 `include_expired=true` 参数查看过期公告

### 2. 优先级排序
- 公告按优先级（priority）降序排列
- 相同优先级按创建时间排序
- 优先级数字越大，显示越靠前

### 3. 类型筛选
- 支持按类型筛选：info（信息）、warning（警告）、urgent（紧急）
- 可扩展支持更多类型

### 4. 数量限制
- 默认最多显示10条公告
- 可通过配置文件调整 `max_display` 值
- API支持 `limit` 参数动态控制

## 🛡️ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查Authorization token |
| 404 | 公告不存在 | 确认公告ID是否正确 |
| 500 | 服务器错误 | 检查JSON文件格式或服务器状态 |

### 错误响应格式
```json
{
  "success": false,
  "code": 400,
  "message": "不支持的公告类型: invalid",
  "timestamp": "2025-08-01T15:30:00Z"
}
```

## 🔄 集成说明

### 1. 路由注册
公告服务已在 `main.py` 中注册：
```python
app.include_router(announcement_app, prefix='/announcements', tags=['公告管理'])
```

### 2. 依赖项
- FastAPI
- 项目现有的token验证系统
- 统一响应格式模块

### 3. 配置要求
确保 `config/announcements.json` 文件存在且格式正确。如果文件不存在，系统会返回空的公告列表。

## 📈 扩展建议

### 未来可能的扩展功能
1. **管理接口**：添加创建、更新、删除公告的API接口
2. **用户权限**：区分普通用户和管理员权限
3. **公告分类**：支持更细粒度的公告分类
4. **阅读状态**：记录用户的公告阅读状态
5. **推送通知**：结合WebSocket实现实时公告推送

### 性能优化
1. **缓存机制**：对频繁访问的公告数据进行缓存
2. **分页支持**：当公告数量较多时支持分页查询
3. **索引优化**：为大量公告数据建立索引

## 🎉 总结

公告系统已成功集成到AIChat_Back项目中，提供了：

- ✅ **轻量级存储**：基于JSON文件，便于维护
- ✅ **完整的API接口**：支持列表查询、详情查看、条件筛选
- ✅ **统一的响应格式**：遵循项目现有标准
- ✅ **灵活的配置**：支持类型、优先级、过期时间等配置
- ✅ **权限控制**：集成现有的token验证机制

系统设计简洁高效，特别适合小型项目的公告管理需求。通过直接编辑JSON文件即可快速更新公告内容，无需复杂的后台管理界面。
