{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/APP/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/APP/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/APP/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/APP/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-aef89164d481a0e4b19e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-70a8a7f0a3c4e41276f1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-84e8f744caa02e7c00a7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-70a8a7f0a3c4e41276f1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-84e8f744caa02e7c00a7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-aef89164d481a0e4b19e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}