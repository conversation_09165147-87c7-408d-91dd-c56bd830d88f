name: aichat
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 0.0.1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  #  AI部分依赖
  langchain_openai: ^0.7.3
  langchain: ^0.7.7+2
  tiktoken_tokenizer_gpt4o_o1: ^1.2.1
  #  本地数据存储依赖
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  #  状态管理
  get: ^4.6.6
  #  网络请求
  dio: ^5.7.0
  #  下拉菜单
  pull_down_button: ^0.10.2
  # 吐司弹窗
  toastification: ^3.0.0
  # 图标
  iconsax_plus: ^1.0.0
  # ios图标
  cupertino_icons: ^1.0.6
  # 处理prompt加密解密的部分
  encrypt:
  fernet:
  base64:
  # dio封装部分
  common_utils:
#  becoin:
  # md文档效果
  flutter_markdown:
  # 处理URL链接
  url_launcher: ^6.2.5
  # 检查版本更新
  package_info_plus:
  # 日历组件
  table_calendar:

  #  计算token
#  flutter_tiktoken:
#    git:
#      url: https://mirror.ghproxy.com/github.com/JerryFans/flutter_tiktoken.git
#      ref: main
  #  ui
  toggle_switch: ^2.3.0

  # 主题部分
  flex_color_scheme: ^8.2.0
  google_fonts: ^6.2.1

  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.


dev_dependencies:
  flutter_test:
    sdk: flutter
  #  本地数据储存依赖
  hive_generator: ^1.1.3
  build_runner: ^2.1.11
  #
  flutter_svg:
  #  chat_bubbles: ^1.6.0
  # 信息列表
  flutter_im_list:
    git:
      url: https://github.com/crazycodeboy/flutter_im_list.git

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/views/pexels-iriser-1379636.jpg
    - assets/views/pexels-maoriginalphotography-1485894.jpg
    - assets/views/pexels-pok-rie-33563-2049422.jpg
    - assets/views/tweet_selfie_ame_home_002.png
    - assets/emojis/idc.png
    - assets/emojis/die.png
    - assets/emojis/cry.png
    - assets/emojis/sorry.png
    - assets/emojis/love.png
    - assets/emojis/ok.png
    - assets/emojis/omg.png
    - assets/emojis/this.png
    - assets/background/jine.png
    - assets/background/jine_emoji_bg.png
    - assets/background/jine_avatar.jpg
    - fonts/
    - assets/background/kuromi/background.png
    - assets/background/bear/background.jpg


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: dripicons
      fonts:
        - asset: fonts/icons/dripicons.ttf
    - family: Zpix
      fonts:
        - asset: fonts/Zpix.ttf
    - family: LXGW
      fonts:
        - asset: fonts/LXGWBrightGB-Medium.ttf
    #
    # For details regarding fonts from package dependencies,
    # see https://flutter.dev/custom-fonts/#from-packages
