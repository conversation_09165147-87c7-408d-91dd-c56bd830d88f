import 'package:aichat/core/controllers/data_controller.dart';
import 'package:aichat/core/controllers/page/forget_password_controller.dart';
import 'package:get/get.dart';

import '../controllers/character_controller.dart';
import '../controllers/init_task.dart';
import '../controllers/page/login_controller.dart';
import '../controllers/page/profile_controller.dart';
import '../controllers/page/register_controller.dart';

class AllBinding implements Bindings {
  @override
  void dependencies() {
    Get.put(DataController());
    Get.put(InitTask());
    Get.put(LoginController());
    Get.put(CharacterController());
    Get.lazyPut(() => ProfileController());
    Get.lazyPut(() => RegisterController());
    Get.lazyPut(() => ForgetPasswordController());
  }
}
