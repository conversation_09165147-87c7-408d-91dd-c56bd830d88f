# 签到功能 API 文档

## 概述

签到功能允许用户每日签到获取随机额度奖励，奖励会直接添加到用户的API余额中。每个用户每天只能签到一次。

## 奖励机制

签到奖励采用随机算法，概率分布如下：

| 奖励范围 | 概率 | 说明 |
|---------|------|------|
| 0-2 额度 | 90% | 基础奖励 |
| 2-3 额度 | 6% | 中等奖励 |
| 3-4 额度 | 4% | 高级奖励 |

- 奖励精确到小数点后2位
- 奖励直接添加到用户API余额中

## API 接口

### 1. 每日签到

**接口地址：** `POST /checkin/`

**请求头：**
```
Authorization: Bearer <用户登录token>
Content-Type: application/json
```

**请求参数：**
```json
{
  "username": "<EMAIL>",
  "token": "用户的API密钥token"
}
```

**成功响应：**
```json
{
  "code": 200,
  "message": "签到成功！",
  "data": {
    "reward_amount": 1.25,
    "checkin_date": "2025-08-02",
    "checkin_id": 1
  }
}
```

**错误响应：**
```json
{
  "code": 400,
  "message": "今天已经签到过了，请明天再来！"
}
```

### 2. 签到历史记录

**接口地址：** `GET /checkin/history`

**请求头：**
```
Authorization: Bearer <用户登录token>
```

**查询参数：**
- `limit` (可选): 返回记录数量，默认10条

**请求示例：**
```
GET /checkin/history?limit=20
```

**成功响应：**
```json
{
  "code": 200,
  "message": "获取签到历史成功",
  "data": {
    "history": [
      {
        "checkin_date": "2025-08-02",
        "reward_amount": 1.25,
        "checkin_time": "2025-08-02 09:30:15"
      },
      {
        "checkin_date": "2025-08-01",
        "reward_amount": 2.80,
        "checkin_time": "2025-08-01 10:15:30"
      }
    ],
    "total_checkin_days": 2,
    "total_rewards": 4.05
  }
}
```

### 3. 签到状态查询

**接口地址：** `GET /checkin/status`

**请求头：**
```
Authorization: Bearer <用户登录token>
```

**请求参数：**
```json
{
  "username": "<EMAIL>",
  "token": "用户的API密钥token"
}
```

**成功响应：**
```json
{
  "code": 200,
  "message": "获取签到状态成功",
  "data": {
    "has_checked_in_today": false,
    "can_checkin": true
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误或今日已签到 |
| 401 | 认证失败，token无效 |
| 500 | 服务器内部错误 |
| 502 | 获取用户信息失败 |
| 503 | 外部服务连接失败 |
| 504 | 请求超时 |

## 使用示例

### JavaScript/Fetch 示例

```javascript
// 执行签到
async function dailyCheckin() {
  try {
    const response = await fetch('/checkin/', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer your-login-token',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        token: 'your-api-token'
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`签到成功！获得 ${result.data.reward_amount} 额度`);
    } else {
      console.log(`签到失败：${result.message}`);
    }
  } catch (error) {
    console.error('签到请求失败:', error);
  }
}

// 查询签到状态
async function checkStatus() {
  try {
    const response = await fetch('/checkin/status', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer your-login-token',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: '<EMAIL>',
        token: 'your-api-token'
      })
    });
    
    const result = await response.json();
    
    if (result.data.can_checkin) {
      console.log('今日可以签到');
    } else {
      console.log('今日已签到');
    }
  } catch (error) {
    console.error('查询状态失败:', error);
  }
}
```

### Python/Requests 示例

```python
import requests

# 签到
def daily_checkin():
    url = "http://your-domain.com/checkin/"
    headers = {
        "Authorization": "Bearer your-login-token",
        "Content-Type": "application/json"
    }
    data = {
        "username": "<EMAIL>",
        "token": "your-api-token"
    }
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    if result["code"] == 200:
        print(f"签到成功！获得 {result['data']['reward_amount']} 额度")
    else:
        print(f"签到失败：{result['message']}")

# 查询历史
def get_checkin_history():
    url = "http://your-domain.com/checkin/history?limit=10"
    headers = {
        "Authorization": "Bearer your-login-token"
    }
    data = {
        "username": "<EMAIL>",
        "token": "your-api-token"
    }
    
    response = requests.get(url, headers=headers, json=data)
    result = response.json()
    
    if result["code"] == 200:
        history = result["data"]["history"]
        print(f"签到历史：共 {len(history)} 次")
        for record in history:
            print(f"日期：{record['checkin_date']}，奖励：{record['reward_amount']}")
```

## 注意事项

1. **每日限制**：每个用户每天只能签到一次，重复签到会返回错误
2. **时区**：签到日期基于服务器时区，通常为UTC+8
3. **余额更新**：签到奖励会立即添加到用户API余额中
4. **认证要求**：所有接口都需要有效的登录token
5. **API密钥**：需要提供有效的用户API密钥token

## 数据库结构

签到记录存储在 `checkins` 表中：

```sql
CREATE TABLE `checkins` (
  `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
  `username` VARCHAR(258) NOT NULL COMMENT '签到用户的邮箱',
  `checkin_date` DATE NOT NULL COMMENT '签到日期',
  `reward_amount` INT NOT NULL COMMENT '签到获得的额度',
  `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '签到时间',
  UNIQUE KEY `uid_checkins_usernam_b0e6e8` (`username`, `checkin_date`)
) CHARACTER SET utf8mb4;
```

## 部署说明

1. 确保数据库迁移已执行：
   ```bash
   python -m aerich upgrade
   ```

2. 重启应用服务器以加载新的路由

3. 验证接口可用性：
   ```bash
   curl -X GET http://localhost:8700/checkin/status \
     -H "Authorization: Bearer your-token" \
     -H "Content-Type: application/json" \
     -d '{"username":"<EMAIL>","token":"test-token"}'
   ```
