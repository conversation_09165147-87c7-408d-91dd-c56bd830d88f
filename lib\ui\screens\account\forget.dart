import 'package:aichat/core/controllers/page/forget_password_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class ForgetPasswordPage extends GetView<ForgetPasswordController> {
  const ForgetPasswordPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('忘记密码'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 顶部Logo
                Center(
                  child: Container(
                    height: 100,
                    width: 100,
                    margin: const EdgeInsets.symmetric(vertical: 20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).shadowColor.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Image.asset(
                        "assets/emojis/cry.png",
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),

                // 标题
                Text(
                  "重置密码",
                  style: Theme.of(context)
                      .textTheme
                      .headlineSmall
                      ?.copyWith(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  "请输入您的邮箱地址，我们将发送验证码帮助您重置密码",
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // 邮箱输入框
                TextField(
                  controller: controller.emailController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.email_outlined),
                    hintText: '请输入您的电子邮箱',
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),

                // 验证码输入框和发送按钮
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: controller.verificationCodeController,
                        decoration: const InputDecoration(
                          prefixIcon: Icon(Icons.security_outlined),
                          hintText: '请输入验证码',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Obx(() => ElevatedButton(
                          onPressed: controller.isLoading.value
                              ? null
                              : controller.isSentCode.value
                                  ? null
                                  : controller.sendVerificationCode,
                          child: controller.isLoading.value
                              ? const Text("发送中")
                              : controller.isSentCode.value
                                  ? Text(
                                      "${controller.count.value}秒",
                                    )
                                  : const Text("发送验证码"),
                        )),
                  ],
                ),
                const SizedBox(height: 16),

                // 新密码输入框
                TextField(
                  controller: controller.passwordController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.lock_outline),
                    hintText: '请输入新密码（8位以上）',
                  ),
                  obscureText: true,
                ),
                const SizedBox(height: 16),

                // 确认密码输入框
                TextField(
                  controller: controller.confirmPasswordController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.lock_outline),
                    hintText: '请再次输入新密码',
                  ),
                  obscureText: true,
                ),
                const SizedBox(height: 16),

                // 错误信息显示
                Obx(() => controller.hasError.value
                    ? Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          controller.errorMessage.value,
                          style: TextStyle(
                            color:
                                Theme.of(context).colorScheme.onErrorContainer,
                            fontSize: 14,
                          ),
                        ),
                      )
                    : const SizedBox.shrink()),

                // 服务条款和隐私政策
                Row(
                  children: [
                    Obx(() => Checkbox(
                          value: controller.agreeToTerms.value,
                          onChanged: (value) =>
                              controller.agreeToTerms.value = value ?? false,
                        )),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => controller.agreeToTerms.value =
                            !controller.agreeToTerms.value,
                        child: MarkdownBody(
                          data:
                              "已阅读并且同意与你的 [服务条款](https://example.com/terms) 和 [隐私政策](https://example.com/privacy)",
                          onTapLink: (text, href, title) {
                            if (href != null) {
                              launchUrl(Uri.parse(href));
                            }
                          },
                          styleSheet:
                              MarkdownStyleSheet.fromTheme(Theme.of(context))
                                  .copyWith(
                            p: Theme.of(context).textTheme.bodyMedium,
                            a: TextStyle(
                                color: Theme.of(context).colorScheme.primary),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // 重置密码按钮
                Obx(() => ElevatedButton(
                      onPressed: controller.isLoading.value
                          ? null
                          : controller.resetPassword,
                      child: controller.isLoading.value
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text("重置密码"),
                    )),

                // 返回登录
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "记起密码了？",
                        style: TextStyle(color: Colors.grey),
                      ),
                      GestureDetector(
                        onTap: controller.goToLogin,
                        child: const Text(
                          "返回登录",
                          style: TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
