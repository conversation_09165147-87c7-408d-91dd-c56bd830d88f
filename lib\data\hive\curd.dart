// 保存登录后返回的 token key key_id 登录状态 用户名 到本地存储
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../core/controllers/page/login_controller.dart';
import '../const.dart';

// 储存登录的信息
Future<void> saveLogin(Map data, String userName) async {
  LoginController controller = Get.find();

  await Hive.openBox(ConstData.settingBox);
  Box settingBox = Hive.box(ConstData.settingBox);

  await settingBox.put(ConstData.userName, userName);
  await settingBox.put(ConstData.tokenKey, data['token']);
  await settingBox.put(ConstData.keyValue, 'sk-${data['key_value']}');
  await settingBox.put(ConstData.isLogin, true);
  await settingBox.put(ConstData.baseUrl, data['base_url']);
  controller.isLogin.value = true;
}

// 退出登录,并且移除相关的信息
Future<void> cleanLogin() async {
  LoginController controller = Get.find();
  await Hive.openBox(ConstData.settingBox);
  Box settingBox = Hive.box(ConstData.settingBox);
  await settingBox.deleteAll([
    ConstData.tokenKey,
    ConstData.keyValue,
    ConstData.isLogin,
    ConstData.baseUrl
  ]);
  controller.isLogin.value = false;
  print('移除登录数据成功');
}

// 从本地存储获取 token
Future<String?> getToken() async {
  await Hive.openBox(ConstData.settingBox);
  Box settingBox = Hive.box(ConstData.settingBox);
  String? token = await settingBox.get(ConstData.tokenKey);

  return token;
}

// 储存查询到的key信息
saveKeyInfo(Map data) async {
  await Hive.openBox(ConstData.settingBox);
  Box settingBox = Hive.box(ConstData.settingBox);

  // 储存信息
  settingBox.put(ConstData.keyAllQuota, data['remain_quota']);
  settingBox.put(ConstData.keyUsedQuota, data['used_quota']);

  print('当前储存的信息如下：${data['used_quota']},${data['remain_quota']}');
}
