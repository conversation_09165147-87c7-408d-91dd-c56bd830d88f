import 'package:aichat/core/controllers/messages_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_im_list/models/message_model.dart';
import 'package:get/get.dart';
import 'package:pull_down_button/pull_down_button.dart';

import '../toast/toast.dart';

class PullDown {
  /// 本类用于返回一个信息长按界面的下拉按钮
  /// 1. 需要传递相应的气泡信息
  /// 2. 显示正确的位置
  /// 3. messagesList是需要传递一个控制显示界面的类，确保删除操作所控制的界面和当前显示的界面使用的是同一个示例，不能在本类
  /// 再实例化继承一个messagesList类然后进行操作

  final MessageModel message;
  final BuildContext context;
  // 获取界面的控制器，用于删除消息
  MessagesController messagesController = Get.find();

  PullDown({required this.message, required this.context});

  showPullDown(position) async {
    // print('当前点击位置：${position}');
    return await showPullDownMenu(
        context: context,
        items: [
          PullDownMenuItem(
            onTap: () {
              messagesController.rewriteMessage(message);
              // 输入框内填写选择回溯的信息的文本
              if (message.ownerType == OwnerType.sender) {
                messagesController.textEditingController.text =
                    message.content.toString();
                messagesController.currentText.value =
                    message.content.toString();
              }
            },
            title: '回溯',
            icon: CupertinoIcons.backward,
          ),
          PullDownMenuItem(
            onTap: () {
              // 复制文本到剪贴板
              _showCopyDialog();
            },
            title: '复制',
            icon: CupertinoIcons.doc_on_doc,
          ),
        ],
        position: position);
  }

  void _showCopyDialog() {
    Get.dialog(
      CupertinoAlertDialog(
        title: const Text('复制内容'),
        content: SingleChildScrollView(
          child: SelectableText(
            message.content,
            textAlign: TextAlign.start,
          ),
        ),
        actions: <Widget>[
          CupertinoDialogAction(
            child: const Text('复制全部'),
            onPressed: () {
              Clipboard.setData(ClipboardData(text: message.content));
              Get.back();
              Toast.showSuccess('复制成功', '所选内容已复制到粘贴板');
            },
          ),
          CupertinoDialogAction(
            child: const Text('关闭'),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
      barrierDismissible: true,
    );
  }
}
