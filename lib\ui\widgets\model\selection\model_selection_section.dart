// {{ AURA-X: Add - 创建模型选择区域主组件. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/page/profile_controller.dart';
import '../detail/model_detail_page.dart';
import 'model_card_simple.dart';

class ModelSelectionSection extends StatelessWidget {
  final ProfileController controller;

  const ModelSelectionSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和查看详细按钮
        _buildSectionHeader(context),
        
        const SizedBox(height: 16),
        
        // 模型卡片列表
        _buildModelCards(context),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '选择模型',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        TextButton.icon(
          onPressed: () => _navigateToDetailPage(context),
          icon: Icon(
            Icons.info_outline,
            size: 18,
            color: Theme.of(context).primaryColor,
          ),
          label: Text(
            '查看详细',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }

  Widget _buildModelCards(BuildContext context) {
    return Obx(() {
      if (controller.models.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 60),
            child: Column(
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(
                  '正在加载模型列表...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      }

      return SizedBox(
        height: 200, // 减少高度，简化显示
        child: PageView.builder(
          controller: PageController(viewportFraction: 0.85),
          itemCount: controller.models.length,
          itemBuilder: (context, index) {
            final model = controller.models[index];
            return ModelCardSimple(
              model: model,
              controller: controller,
            );
          },
        ),
      );
    });
  }

  void _navigateToDetailPage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ModelDetailPage(controller: controller),
      ),
    );
  }
}
