# 🏗️ 系统架构总览

> **创建时间**: 2025-08-03 22:57:06 +08:00  
> **系统版本**: Flutter AI Chat v1.0  
> **架构模式**: MVC + 响应式状态管理

## 📐 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                             │
├─────────────────────────────────────────────────────────────┤
│  ChatScreen  │  HomePage  │  ProfilePage  │  SettingPage   │
├─────────────────────────────────────────────────────────────┤
│                    Controller Layer                         │
├─────────────────────────────────────────────────────────────┤
│ MessagesController │ CharacterController │ LoginController │
├─────────────────────────────────────────────────────────────┤
│                     Service Layer                           │
├─────────────────────────────────────────────────────────────┤
│    Gpt Service    │   Client Service   │   DioService     │
├─────────────────────────────────────────────────────────────┤
│                      Data Layer                             │
├─────────────────────────────────────────────────────────────┤
│   Hive Storage    │   API Models      │   Constants      │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心模块分析

### 1. UI层 (Presentation Layer)

**主要组件**:
- `ChatScreen` - 聊天界面核心容器
- `TextEdit` - 消息输入组件
- `ToolsBar` - 工具栏组件
- `ThemeSwitcher` - 主题切换组件

**设计特点**:
- 响应式UI设计
- 主题化支持
- 组件化架构
- 性能优化

### 2. 控制器层 (Controller Layer)

**核心控制器**:

#### MessagesController
```dart
class MessagesController extends GetxController {
  // 聊天列表控制器
  ChatController controller = ChatController();
  
  // 状态管理
  RxBool isSending = false.obs;
  RxBool isGptReady = false.obs;
  RxBool isCalculatingTokens = false.obs;
  
  // 消息数据
  RxList chatMessagesCopy = <MessageModel>[].obs;
  Rx<List<ChatMessage>> chatMessagesToGPT = <ChatMessage>[].obs;
}
```

#### CharacterController
```dart
class CharacterController extends GetxController {
  // 当前角色ID
  RxInt currentCharacterId = 0.obs;
  
  // 角色设置
  Map<int, AICharacter> characterSettings = {};
  
  // 页面索引
  RxInt currentIndex = 0.obs;
}
```

### 3. 服务层 (Service Layer)

#### AI服务 (Gpt)
```dart
class Gpt {
  // OpenAI客户端
  late ChatOpenAI chatbot;
  
  // 核心功能
  Future initGPT() async { /* 初始化GPT */ }
  Future getReply() async { /* 获取AI回复 */ }
  Future<List<ChatMessage>> getHistory() async { /* 获取历史记录 */ }
  Future<void> preloadJailBreak() async { /* 预加载JailBreak */ }
}
```

#### 网络服务 (Client)
```dart
class Client {
  Dio dio = DioService.instance;
  
  // API接口
  Future<void> getPrompt() async { /* 获取提示词 */ }
  Future<APIModel> login(String username, String password) async { /* 登录 */ }
  Future<APIModel> getKeyInfo(String userName, String key) async { /* 获取密钥信息 */ }
}
```

### 4. 数据层 (Data Layer)

#### 数据模型
```dart
// UI展示模型
class MessageModel {
  final String? avatar;
  final int id;
  final OwnerType ownerType;
  final dynamic content;
  final int createdAt;
}

// API响应模型
class APIModel {
  final bool success;
  final int code;
  final String message;
  final dynamic data;
  final String? timestamp;
}
```

#### 本地存储
```dart
// Hive存储管理
class DataController extends GetxController {
  late Box settingBox;
  late Box messageBox;
  
  Future initMessageBox(int characterId) async { /* 初始化消息存储 */ }
  Future addMessages(MessageModel message) async { /* 添加消息 */ }
  List<MessageModel> getMessages() { /* 获取消息列表 */ }
}
```

## 🔄 数据流转机制

### 消息发送流程
```
用户输入 → TextEdit → MessagesController.addMessage() 
         ↓
MessageModel创建 → 添加到UI列表 → 本地存储
         ↓
触发AI回复 → Gpt.getReply() → ChatOpenAI API调用
         ↓
AI响应 → MessageModel创建 → 添加到UI列表 → 本地存储
```

### 数据转换链路
```
MessageModel (UI层) ↔ ChatMessage (AI层)
         ↓
通过MessagesController.chatMessagesToGPT转换
         ↓
传递给Gpt.getHistory()进行AI处理
```

## 🎨 状态管理架构

### GetX响应式编程
```dart
// 状态定义
RxBool isSending = false.obs;

// 状态监听
ever(chatMessagesCopy, (callback) {
  chatMessagesToGPT.value = chatMessagesCopy.map((e) {
    // 数据转换逻辑
  }).toList();
});

// UI响应
Obx(() => messagesController.isSending.value 
  ? Text('对方正在输入...') 
  : Text('角色名称'))
```

### 依赖注入管理
```dart
class AllBinding implements Bindings {
  @override
  void dependencies() {
    Get.put(DataController());
    Get.put(InitTask());
    Get.put(LoginController());
    Get.put(CharacterController());
    Get.lazyPut(() => ProfileController());
  }
}
```

## 🔌 网络架构

### Dio拦截器链
```dart
// 请求拦截器
class OnRequestInterceptor extends Interceptor {
  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 认证检查
    // Token添加
    // 受保护端点验证
  }
}

// 响应拦截器
class OnResponseInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 自动模型转换
    // 响应数据处理
  }
}

// 错误拦截器
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException e, ErrorInterceptorHandler handler) {
    // 错误分类处理
    // Toast提示
    // 重试机制
  }
}
```

## 🚀 性能优化策略

### 1. 异步处理
- Token计算异步化（避免UI阻塞）
- JailBreak预加载（减少首次延迟）
- 消息加载异步化（提升响应速度）

### 2. 内存管理
- 消息列表分页加载
- 图片资源懒加载
- 控制器生命周期管理

### 3. 网络优化
- 请求拦截器优化
- 错误重试机制
- 连接池管理

## 🔒 安全架构

### 认证机制
```dart
// Token验证
Future<String?> getToken() async {
  Box settingBox = Hive.box(ConstData.settingBox);
  return await settingBox.get(ConstData.tokenKey);
}

// 受保护端点检查
bool _isProtectedEndpoint(String path) {
  return protectedEndpoints.any((endpoint) => path.startsWith(endpoint));
}
```

### 数据加密
```dart
// 提示词加密存储
Map prompt = EncryptionUtil.decryptData(result.data.toString());
settingBox.put(ConstData.promptKey, prompt);
```

## 📱 主题系统架构

### 主题配置
```dart
class ChatThemeConfig {
  final Color? backgroundColor;
  final String? backgroundImage;
  final String? inputBackgroundImage;
  final Color? appBarColor;
  final double appBarHeight;
  final bool showEmojiBar;
}
```

### 动态主题加载
```dart
Future<ChatThemeConfig> loadThemeForCharacter(int characterId) async {
  // 角色专属主题优先级
  // 系统主题回退机制
  // 主题缓存管理
}
```

---

**架构演进**: 本架构支持模块化扩展，可根据业务需求灵活调整各层组件
