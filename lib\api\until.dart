import 'dart:convert';

import 'package:fernet/fernet.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../data/models/api_defult_model.dart';
import 'client.dart';

class EncryptionUtil {
  static const String _secretKey =
      'f457d0edc1520348178161f1b6aa7e479c36709f65367035f20179a5dae11353'; // 这里使用您后端的SECRET_KEY

  /// 解密数据
  static Map<String, dynamic> decryptData(String encryptedData) {
    try {
      // 从secretKey生成32位base64编码的key
      final keyBytes = utf8.encode(_secretKey).sublist(0, 32);
      final base64Key = base64.encode(keyBytes);

      // 创建Fernet实例
      final fernet = Fernet(base64Key);

      // 解密数据
      final decryptedBytes = fernet.decrypt(encryptedData);
      final decryptedString = utf8.decode(decryptedBytes);

      // 将JSON字符串转换回Map
      return json.decode(decryptedString);
    } catch (e) {
      print('解密失败: $e');
      throw Exception('数据解密失败');
    }
  }
}

/// 版本检查类，用于检查应用版本并在有新版本时显示更新弹窗
class CheckVersion {
  late String currentVersion;

  /// 初始化并检查版本
  Future<void> checkForUpdates() async {
    try {
      // 获取当前应用版本
      await _getCurrentVersion();

      // 获取服务器最新版本信息
      await _checkServerVersion();
    } catch (e) {
      print('版本检查错误: $e');
    }
  }

  /// 获取当前应用版本
  Future<void> _getCurrentVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    currentVersion = packageInfo.version;
    print('当前应用版本: $currentVersion');
  }

  /// 获取服务器版本信息并比较
  Future<void> _checkServerVersion() async {
    Client client = Client();
    try {
      APIModel response = await client.getVersion();

      // {{ AURA-X: Modify - 更新字段名以匹配新API格式. Approval: 寸止(ID:2025-07-29T21:54:03+08:00). }}
      if (response.isSuccess && response.data != null) {
        Map<String, dynamic> versionData = response.data;

        String latestVersion = versionData['latest_version'];
        bool isForceUpdate = versionData['is_force_update'];
        String updateTitle = versionData['update_title'];
        String updateDescription = versionData['update_description'];
        String downloadUrl = versionData['download_url'];
        String minSupportedVersion = versionData['min_supported_version'];

        print('服务器最新版本: $latestVersion');
        print('当前版本: $currentVersion');

        // 比较版本号
        if (_isNewerVersion(latestVersion, currentVersion)) {
          // 显示更新弹窗
          _showUpdateDialog(
            latestVersion,
            isForceUpdate,
            updateTitle,
            updateDescription,
            downloadUrl,
          );
        } else {
          print('当前已是最新版本');
        }
      }
    } catch (e) {
      print('获取版本信息失败: $e');
    }
  }

  /// 比较版本号，检查是否有新版本
  bool _isNewerVersion(String latestVersion, String currentVersion) {
    List<int> latest =
        latestVersion.split('.').map((e) => int.parse(e)).toList();
    List<int> current =
        currentVersion.split('.').map((e) => int.parse(e)).toList();

    // 确保两个列表长度相同
    while (latest.length < current.length) latest.add(0);
    while (current.length < latest.length) current.add(0);

    // 逐位比较版本号
    for (int i = 0; i < latest.length; i++) {
      if (latest[i] > current[i]) return true;
      if (latest[i] < current[i]) return false;
    }

    return false; // 版本相同
  }

  /// 显示更新弹窗
  void _showUpdateDialog(
    String latestVersion,
    bool isForceUpdate,
    String updateTitle,
    String updateDescription,
    String downloadUrl,
  ) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Get.theme.colorScheme.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          updateTitle,
          style: TextStyle(
            color: Get.theme.colorScheme.onSurface,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.new_releases,
                      color: Get.theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '发现新版本: $latestVersion',
                      style: TextStyle(
                        color: Get.theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '更新内容：',
                style: TextStyle(
                  color: Get.theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.surfaceContainer,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Get.theme.colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Text(
                  updateDescription,
                  style: TextStyle(
                    color: Get.theme.colorScheme.onSurfaceVariant,
                    fontSize: 13,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          if (!isForceUpdate) // 非强制更新时显示"稍后再说"按钮
            TextButton(
              onPressed: () {
                Get.back();
              },
              style: TextButton.styleFrom(
                foregroundColor: Get.theme.colorScheme.onSurfaceVariant,
              ),
              child: const Text('稍后再说'),
            ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _launchUrl(downloadUrl);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.colorScheme.primary,
              foregroundColor: Get.theme.colorScheme.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('立即更新'),
          ),
        ],
      ),
      barrierDismissible: !isForceUpdate, // 强制更新时不允许点击外部关闭
    );
  }

  /// 打开下载链接
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        print('无法打开链接: $url');
      }
    } catch (e) {
      print('打开链接错误: $e');
    }
  }
}

/// 示例：如何在应用中使用版本检查
///
/// ```dart
/// // 在应用启动时或适当的时机调用
/// void checkAppVersion() {
///   CheckVersion versionChecker = CheckVersion();
///   versionChecker.checkForUpdates();
/// }
///
/// // 可以在应用初始化时调用
/// @override
/// void onInit() {
///   super.onInit();
///   CheckVersion().checkForUpdates();
/// }
/// ```
