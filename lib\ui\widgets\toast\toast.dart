import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:toastification/toastification.dart';

class Toast {
  // 定义boxShadow
  static final List<BoxShadow> lowModeShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      spreadRadius: 1,
      blurRadius: 10,
      offset: const Offset(0, 5),
    ),
  ];

  static showSuccess(String title, String description) {
    return toastification.show(
      type: ToastificationType.success,
      style: ToastificationStyle.flat,
      backgroundColor: Get.theme.colorScheme.surface,
      title: Text(
        title,
        style: TextStyle(
          color: Get.theme.colorScheme.onSurface,
          fontWeight: FontWeight.bold,
        ),
      ),
      description: Text(description),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 1, milliseconds: 500),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
    );
  }

  static showError(String title, String description) {
    return toastification.show(
      type: ToastificationType.error,
      style: ToastificationStyle.flat,
      backgroundColor: Get.theme.colorScheme.surface,
      title: Text(
        title,
        style: TextStyle(
          color: Get.theme.colorScheme.onSurface,
          fontWeight: FontWeight.bold,
        ),
      ),
      description: Text(description),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }

  static showInfo(String title, String description) {
    return toastification.show(
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      backgroundColor: Get.theme.colorScheme.surface,
      title: Text(
        title,
        style: TextStyle(
          color: Get.theme.colorScheme.onSurface,
          fontWeight: FontWeight.bold,
        ),
      ),
      description: Text(description),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 1, milliseconds: 500),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }
}
