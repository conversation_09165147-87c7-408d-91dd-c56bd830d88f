### API 部分代码解析

#### client.dart
处理链接api的类，功能如下：
1. 与api进行交互（使用try进行拦截报错）
2. 获取信息后进行本地储存（思考api与储存是否需要进行单独的功能分割？）
3. 大部分接口在本地用户未登录的情况下都不调用

##### getPrompt
获取api接口传递的所有prompt，其中prompt为加密信息。
功能详细解析： 
获取到云端的prompt，随后使用`EncryptionUtil`进行解密，再储存在box`ConstData.settingBox`之中

疑惑与准备：
是否需要对dio接口进行**封装**处理？
封装有利于学习dio整体的框架结构，不封装也能正常使用。

##### getModels
从api接口获取所有模型，其中本地是以云端**原格式**进行储存模型信息。
储存方式为map，格式如下：
```json
{
    'name': 'deepseek',
    'price': 0.5,
    'descriptions': '一个普通的国产大模型',
    'maxToken':500000
},
{
    'name': 'gemini',
    'price': 0.8,
    'descriptions': '一个普通的谷歌公司大模型',
    'maxToken':12500000
},
```
功能详细解析：
该类使用`ConstData.allModels`储存从云端获取到的所有模型，并且会判断是否本地存在已选择的默认模型，
否则会使用模型中的第一个来做初始化模型，使用`ConstData.allModels`进行储存。
这个细节是为了保证ai/gpt类中存在默认对话模型，其中本方法会在多个场景调用，分别是：
登录后打开软件时 / 登录后点击“我的”界面时 / ....

疑惑与准备：
软件中出现了多次hive相关的初始化和调用的操作，是否可以整合成为一个方法来直接调用呢？
TODO: 未来去了解flutter项目里如何规范化开发，以及dio相关的使用处理技巧

##### register
接受一个email数据，然后调用发送api请求，其中返回的map类型为有关调用结果的数据。
其中处理map数据的方法在其他类中实现。

##### verifyRegister
邮箱验证账号，需要传递验证码以及账号相关的信息。
其中本方法专注与去与api交互，处理放回信息的代码在其他方法中实现。

##### login
登录账号。
登录成功后会执行：
```dart
        await saveLogin(result['data'],username);
        await getPrompt();
        await getModels();
```
其中会把放回的信息都储存起来，并且获取一次云端所有prompt，还会获取所有模型信息

##### 其余API交互
分别有`resetPassword`重置密码，`forgetPassword`忘记密码，`getKeyInfo`查询密钥信息，`addKeyQuota`添加额度，
`getVersion`获取版本更新json



















