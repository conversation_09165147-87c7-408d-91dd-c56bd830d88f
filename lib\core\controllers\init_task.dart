import 'package:aichat/api/client.dart';
import 'package:get/get.dart';

import '../../api/until.dart';
import '../services/announcement_service.dart';

// 本类主要执行各种不同的软件初始化时需要运行的任务
// 1. 尝试从云端获取所有角色的提示词,获取成功则储存本地,获取失败则从本地读取
// 2. 获取软件的最新版本,检测更新,并且弹出软件更新界面
// 3. 检测用户是否存在本地的key,不存在的话则重新获取(或者选择ai部分报错的时候再进行相关的处理)
// 4. 检查并显示公告信息

class InitTask extends GetxController {
  @override
  Future<void> onInit() async {
    super.onInit();
    // 从云端获取信息
    Client re = Client();
    re.getPrompt();
    re.getModels();
    // 获取新版本信息
    CheckVersion versionChecker = CheckVersion();
    await versionChecker.checkForUpdates();

    // {{ AURA-X: Add - 在初始化时检查并显示公告. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
    // 检查并显示公告（延迟一点时间，确保界面已经加载完成）
    Future.delayed(const Duration(milliseconds: 1500), () {
      AnnouncementService.checkAndShowAnnouncements();
    });
  }
}
