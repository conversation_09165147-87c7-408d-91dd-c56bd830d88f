### ai部分代码分析与解析

##### ai_error.dart
专门写ai报错相关的部分，其中根据gpt中的报错内容进行匹配，匹配正确之后，以弹窗形式进行显示报错。

#### gpt.dart
专门处理chatgpt部分的类。

###### initGPT
代码首先进行基础信息初始化，并且获取到本地储存的信息
```dart
    // hive部分初始化
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);

    Map? chatModel = await settingBox.get(ConstData.currentModel);
    String? apiKey = await settingBox.get(ConstData.keyValue);
    String? baseUrl = await settingBox.get(ConstData.baseUrl) + '/v1';
    maxTokens = chatModel?['maxToken'];

    print('当前gpt信息：$baseUrl, $apiKey');
```
其次再对`chatbot`进行赋值。

###### getHistory
获取聊天记录历史，并且转换为ai接受的历史上下文
```dart
    List<ChatMessage> chatHistory = messagesController.chatMessagesCopy.value.map((e) {
      if (e.ownerType == OwnerType.sender) {
        return HumanChatMessage(content: ChatMessageContent.text(e.content.toString()));
      } else {
        return AIChatMessage(content: e.content.toString());
      }
    }).toList();
```
接下来分别是使用`JailBreak`类对历史上下文进行特殊处理，处理结束后的对话，再进行`Tokens`的token计算，避免出现超token的情况。

##### getReply
获取回复信息。
并且通过该行代码获取到ai回复，然后添加到聊天记录中的信息显示控制器中。
`      await messagesController.addMessage(message);
`
使用了`AIErrorHandler`进行报错检测。
##### close
中断获取信息回复。

#### unity.dart
集合了部分`gpt.dart`文件中会使用到的工具类。
##### Token
获取有关token的信息，并且判断是否需要删除聊天记录以确保token不过度。
##### JailBreak
获取角色的破限词，并且插入到聊天记录中
###### initJB
获取本地储存的所有角色的破限提示词
###### jbHistory
读取信息，并且一句本地以及云端提供的角色id，来判断获取那个提示词，并且插入到聊天记录里面。