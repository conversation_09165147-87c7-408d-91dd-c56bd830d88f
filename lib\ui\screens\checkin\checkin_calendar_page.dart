// {{ AURA-X: Add - 创建签到日历界面. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../core/controllers/checkin_controller.dart';
import '../../../data/models/checkin_model.dart';

class CheckinCalendarPage extends StatefulWidget {
  @override
  State<CheckinCalendarPage> createState() => _CheckinCalendarPageState();
}

class _CheckinCalendarPageState extends State<CheckinCalendarPage>
    with TickerProviderStateMixin {
  late CheckinController controller;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    controller = Get.put(CheckinController());

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('每日签到'),
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshData(),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value && controller.checkinHistory.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 签到统计卡片
                _buildStatsCard(),
                const SizedBox(height: 20),

                // 签到日历
                _buildCalendarCard(),
                const SizedBox(height: 20),

                // 选中日期详情
                _buildSelectedDateDetails(),
                const SizedBox(height: 20),

                // 签到按钮
                _buildCheckinButton(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        );
      }),
    );
  }

  // 构建统计卡片
  Widget _buildStatsCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '签到统计',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Obx(() => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      icon: Icons.calendar_today,
                      title: '累计签到',
                      value: '${controller.totalCheckinDays.value}天',
                      color: Colors.blue,
                    ),
                    _buildStatItem(
                      icon: Icons.local_fire_department,
                      title: '连续签到',
                      value: '${controller.consecutiveDays.value}天',
                      color: Colors.orange,
                    ),
                    _buildStatItem(
                      icon: Icons.monetization_on,
                      title: '累计奖励',
                      value:
                          '${controller.totalRewards.value.toStringAsFixed(2)}',
                      color: Colors.green,
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }

  // 构建统计项
  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // 构建日历卡片
  Widget _buildCalendarCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Obx(() => TableCalendar<CheckinModel>(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: controller.currentMonth.value,
              selectedDayPredicate: (day) =>
                  isSameDay(controller.selectedDate.value, day),
              eventLoader: (day) {
                final checkin = controller.getCheckinForDate(day);
                return checkin != null ? [checkin] : [];
              },
              onDaySelected: (selectedDay, focusedDay) {
                controller.selectDate(selectedDay);
              },
              onPageChanged: (focusedDay) {
                controller.currentMonth.value = focusedDay;
              },
              calendarStyle: CalendarStyle(
                outsideDaysVisible: false,
                weekendTextStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                holidayTextStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                selectedDecoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                markerDecoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                markersMaxCount: 1,
              ),
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                titleTextStyle: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                leftChevronIcon: Icon(
                  Icons.chevron_left,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                rightChevronIcon: Icon(
                  Icons.chevron_right,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              daysOfWeekStyle: DaysOfWeekStyle(
                weekdayStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                weekendStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            )),
      ),
    );
  }

  // 构建选中日期详情
  Widget _buildSelectedDateDetails() {
    return Obx(() {
      final selectedCheckin = controller.selectedDateCheckin.value;
      final selectedDate = controller.selectedDate.value;

      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${selectedDate.year}年${selectedDate.month}月${selectedDate.day}日',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (selectedCheckin != null) ...[
                _buildDetailRow(
                  icon: Icons.check_circle,
                  title: '签到状态',
                  value: '已签到',
                  valueColor: Colors.green,
                ),
                const SizedBox(height: 12),
                _buildDetailRow(
                  icon: Icons.access_time,
                  title: '签到时间',
                  value: selectedCheckin.formattedCheckinTime,
                ),
                const SizedBox(height: 12),
                _buildDetailRow(
                  icon: Icons.monetization_on,
                  title: '获得奖励',
                  value: selectedCheckin.formattedRewardAmount,
                  valueColor: Colors.orange,
                ),
              ] else ...[
                _buildDetailRow(
                  icon: Icons.cancel,
                  title: '签到状态',
                  value: '未签到',
                  valueColor: Colors.grey,
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  // 构建详情行
  Widget _buildDetailRow({
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const Spacer(),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: valueColor ?? Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // 构建签到按钮
  Widget _buildCheckinButton() {
    return Obx(() {
      final canCheckin =
          controller.canCheckin.value && !controller.hasCheckedInToday.value;
      final isLoading = controller.isLoading.value;

      return AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: canCheckin && !isLoading
                    ? () async {
                        _animationController.forward().then((_) {
                          _animationController.reverse();
                        });
                        await controller.performCheckin();
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: canCheckin
                      ? Theme.of(context).primaryColor
                      : Theme.of(context).colorScheme.surfaceVariant,
                  foregroundColor: canCheckin
                      ? Colors.white
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: canCheckin ? 4 : 0,
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            controller.hasCheckedInToday.value
                                ? Icons.check_circle
                                : Icons.touch_app,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            controller.hasCheckedInToday.value
                                ? '今日已签到'
                                : '立即签到',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          );
        },
      );
    });
  }
}
