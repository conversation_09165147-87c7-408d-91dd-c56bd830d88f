// {{ AURA-X: Add - 创建模型详细信息页面. Approval: 寸止(ID:2025-08-04). }}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/controllers/page/profile_controller.dart';
import '../shared/model_utils.dart';
import 'model_detail_card.dart';

class ModelDetailPage extends StatefulWidget {
  final ProfileController controller;

  const ModelDetailPage({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<ModelDetailPage> createState() => _ModelDetailPageState();
}

class _ModelDetailPageState extends State<ModelDetailPage> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializePageController();
  }

  void _initializePageController() {
    // 找到当前选中模型的索引
    final currentModelName = widget.controller.currentModelName.value;
    _currentIndex = widget.controller.models.indexWhere(
      (model) => ModelUtils.getModelName(model) == currentModelName,
    );
    if (_currentIndex == -1) _currentIndex = 0;

    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: _buildAppBar(context),
      body: Obx(() {
        if (widget.controller.models.isEmpty) {
          return _buildLoadingState();
        }

        return Column(
          children: [
            // 页面指示器
            _buildPageIndicator(context),
            
            // 模型详细信息
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: widget.controller.models.length,
                itemBuilder: (context, index) {
                  final model = widget.controller.models[index];
                  return ModelDetailCard(
                    model: model,
                    controller: widget.controller,
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        '模型详细信息',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        // 当前模型指示
        Obx(() {
          if (widget.controller.models.isEmpty) return const SizedBox();
          
          final currentModel = widget.controller.models[_currentIndex];
          final isSelected = ModelUtils.isCurrentModel(
            currentModel,
            widget.controller.currentModelName.value,
          );
          
          if (isSelected) {
            return Container(
              margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Text(
                '当前使用',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }
          
          return const SizedBox();
        }),
      ],
    );
  }

  Widget _buildPageIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 左箭头
          IconButton(
            onPressed: _currentIndex > 0 ? _previousPage : null,
            icon: Icon(
              Icons.chevron_left,
              color: _currentIndex > 0
                  ? Theme.of(context).primaryColor
                  : Colors.grey,
            ),
          ),
          
          // 页面指示器点
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                widget.controller.models.length,
                (index) => Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: index == _currentIndex
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade300,
                  ),
                ),
              ),
            ),
          ),
          
          // 右箭头
          IconButton(
            onPressed: _currentIndex < widget.controller.models.length - 1
                ? _nextPage
                : null,
            icon: Icon(
              Icons.chevron_right,
              color: _currentIndex < widget.controller.models.length - 1
                  ? Theme.of(context).primaryColor
                  : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            '正在加载模型详细信息...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _previousPage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextPage() {
    if (_currentIndex < widget.controller.models.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}
