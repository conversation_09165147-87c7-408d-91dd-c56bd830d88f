### message_controller.dart
本文件处理的功能如下：
1. 所有发送信息相关的功能

#### addMessage
`request：MessageModel message`
添加信息的方法，信息会被添加到一下三个变量之中。
1. chatMessagesCopy：一个普通的list，作用是获取信息的序列以及id。
2. controller：该控制器为聊天信息的控制器，添加进去才能显示出来
3. dataController：把信息储存到本地

需要传递的只是信息，类型为`MessageModel`，这是用于显示在聊天界面的信息类型。
如果是ai的信息，则会调用ai的回复，并且把当前设定当前回复之中
```dart
    if (message.ownerType==OwnerType.sender){
      isSending.value = true; // ai正在回复中
      await gpt.initGPT();
      await gpt.getReply();
      isSending.value = false; // 回复结束
    }
```

#### rewriteMessage
回溯信息的功能，其中回溯需要删除三方面的数据： 1.显示 2. 储存 3. ai上下文。
判断进行回复的信息类型：
```dart
    message.ownerType==OwnerType.receiver
        // 选择ai信息回溯时删除它本身以及之后的所有信息
        ? _removeAIMessage(message)
        : _removeUserMessage(message);
```
##### _removeAIMessage
涉及到ai部分的删除信息，只需要删除该信息之后的所有信息即可。
其中进行的步骤如下：
1.  获取当前需要删除的信息id
```dart
    int id = _getMessageID(message)+1;
    int iid = id;
```
2. 随后使用for循环，获取到控制器中对应序列的信息，然后删除，其次在删除复制内容的信息，最后是删除hive中储存的信息。
所有的信息都需要依赖id定位来删除信息，并且当前三种类型：控制器，chatCopy,hive中的信息都一致，所以信息的序列可以通用。
于是当用户需要删除第20条信息的时候，这个“20”的定位，通用可以传递给chatCopy和hive使用。
```dart
    for (id;id<chatMessagesCopy.value.length;id++){
      MessageModel message = chatMessagesCopy.value[id];
      controller.deleteMessage(message);
    }
    // 复制内容也一并删除
    chatMessagesCopy.value.removeRange(iid, chatMessagesCopy.value.length);
    // 储存内容也删除
    dataController.rewriteMessages(iid,true);
```
##### _removeUserMessage
删除用户信息的方法，删除ai信息不同，它需要删除信息本身，删除其之后的所有信息，输入框内重新填充原内容。
所以代码会略有不同：
```dart
    int id = _getMessageID(message)+1;
    int iid = id;
    for (id;id<chatMessagesCopy.value.length;id++){

      MessageModel message = chatMessagesCopy.value[id];
      controller.deleteMessage(message);
    }
    // 复制内容也一并删除
    chatMessagesCopy.value.removeRange(iid, chatMessagesCopy.value.length);
    // 储存内容也删除
    dataController.rewriteMessages(iid,true);
```

#### _getMessageID
获取id的方式，是通过那元素去和list中对比，才得到其元素对应的id。
```dart
    /// 通过对比的方式获取信息在所有信息列表里的序号
     int id = chatMessagesCopy.value.indexOf(message);
     return id;
```

#### chatList
返回的是一个聊天记录列表组件。
首先通过当前角色聊天界面传递的id，来获取其对应的hive储存信息。
然后在把获取到的信息转换，再传递给聊天记录组件，以及chatMessagesCopy之中。
这个只会在首次进入到聊天界面的时候调用，其他时候不会调用，其他时候则是正常通过该组件的控制器来添加信息。









