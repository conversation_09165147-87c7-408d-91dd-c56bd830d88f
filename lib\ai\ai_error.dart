// 错误类型枚举
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:toastification/toastification.dart';

enum AIErrorType {
  quotaExceeded, // 额度用尽
  authenticationError, // 认证错误
  rateLimit, // 请求频率限制
  serverError, // 服务器错误
  networkError, // 网络错误
  requestCancelled, // 请求被取消
  unknownError, // 未知错误
}

// AI错误处理类
class AIErrorHandler {
  // 解析错误，返回错误类型和错误消息
  static (AIErrorType, String) parseError(dynamic exception) {
    try {
      // 尝试将异常转换为字符串并解析
      String exceptionStr = exception.toString();

      // 新增：检查是否是用户主动关闭连接导致的错误
      if (exceptionStr
          .contains('Connection closed before full header was received')) {
        return (AIErrorType.requestCancelled, '对话已由用户停止');
      }

      // 检查是否包含OpenAIClientException
      if (exceptionStr.contains('OpenAIClientException')) {
        // 提取错误代码
        int? errorCode;
        if (exceptionStr.contains('"code":')) {
          final codeMatch = RegExp(r'"code":\s*(\d+)').firstMatch(exceptionStr);
          if (codeMatch != null && codeMatch.groupCount >= 1) {
            errorCode = int.tryParse(codeMatch.group(1) ?? '');
          }
        }

        // 提取错误消息
        String errorMessage = '未知错误';
        if (exceptionStr.contains('"message":')) {
          final messageMatches =
              RegExp(r'"message":\s*"([^"]+)"').allMatches(exceptionStr);
          if (messageMatches.isNotEmpty) {
            // 获取最后一个message，通常是最具体的错误信息
            errorMessage = messageMatches.last.group(1) ?? '未知错误';
          }
        }

        // 根据错误代码和消息判断错误类型
        if (errorCode == 401) {
          return (AIErrorType.authenticationError, errorMessage);
        } else if (errorCode == 429) {
          return (AIErrorType.rateLimit, errorMessage);
        } else if (errorCode != null && errorCode >= 500 && errorCode < 600) {
          return (AIErrorType.serverError, errorMessage);
        }

        // 检查特定错误消息
        if (errorMessage.contains('额度已用尽') ||
            errorMessage.contains('quota exceeded') ||
            errorMessage.contains('insufficient_quota')) {
          return (AIErrorType.quotaExceeded, errorMessage);
        }
      }

      // 检查是否为网络错误
      if (exceptionStr.contains('SocketException') ||
          exceptionStr.contains('Connection refused') ||
          exceptionStr.contains('Network is unreachable')) {
        return (AIErrorType.networkError, '网络连接错误，请检查您的网络连接');
      }

      // 默认为未知错误
      return (AIErrorType.unknownError, '发生未知错误: $exceptionStr');
    } catch (e) {
      // 解析异常时出错
      return (AIErrorType.unknownError, '无法解析错误: ${e.toString()}');
    }
  }

  // 处理错误，显示适当的Toast提示
  static void handleError(dynamic exception, BuildContext context) {
    // 解析错误
    final (errorType, errorMessage) = parseError(exception);

    // 如果是用户取消的请求，则不显示任何提示
    if (errorType == AIErrorType.requestCancelled) {
      print(errorMessage); // 在控制台打印信息，但不打扰用户
      return;
    }

    // 根据错误类型显示不同的Toast提示
    switch (errorType) {
      case AIErrorType.quotaExceeded:
        AIErrorToast.showQuotaExceeded(context, errorMessage);
        break;
      case AIErrorType.authenticationError:
        AIErrorToast.showAuthError(context, errorMessage);
        break;
      case AIErrorType.rateLimit:
        AIErrorToast.showRateLimitError(context, errorMessage);
        break;
      case AIErrorType.serverError:
        AIErrorToast.showServerError(context, errorMessage);
        break;
      case AIErrorType.networkError:
        AIErrorToast.showNetworkError(context, errorMessage);
        break;
      case AIErrorType.unknownError:
        AIErrorToast.showUnknownError(context, errorMessage);
        break;
      case AIErrorType.requestCancelled:
        // TODO: Handle this case.
        throw UnimplementedError();
    }
  }
}

// AI错误Toast提示类
class AIErrorToast {
  // 定义boxShadow
  static final List<BoxShadow> lowModeShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      spreadRadius: 1,
      blurRadius: 10,
      offset: const Offset(0, 5),
    ),
  ];

  // 显示额度用尽提示
  static void showQuotaExceeded(BuildContext context, String message) {
    toastification.show(
      context: context,
      type: ToastificationType.warning,
      style: ToastificationStyle.flat,
      title: const Text("API额度已用尽"),
      description: Text(message),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      icon: const Icon(Iconsax.warning_2),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }

  // 显示认证错误提示
  static void showAuthError(BuildContext context, String message) {
    toastification.show(
      context: context,
      type: ToastificationType.error,
      style: ToastificationStyle.flat,
      title: const Text("认证错误"),
      description: Text(message),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      icon: const Icon(Iconsax.shield_cross),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }

  // 显示请求频率限制提示
  static void showRateLimitError(BuildContext context, String message) {
    toastification.show(
      context: context,
      type: ToastificationType.warning,
      style: ToastificationStyle.flat,
      title: const Text("请求过于频繁"),
      description: Text(message),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      icon: const Icon(Iconsax.timer_1),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }

  // 显示服务器错误提示
  static void showServerError(BuildContext context, String message) {
    toastification.show(
      context: context,
      type: ToastificationType.error,
      style: ToastificationStyle.flat,
      title: const Text("服务器错误"),
      description: Text(message),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      icon: const Icon(Iconsax.cpu_setting),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }

  // 显示网络错误提示
  static void showNetworkError(BuildContext context, String message) {
    toastification.show(
      context: context,
      type: ToastificationType.error,
      style: ToastificationStyle.flat,
      title: const Text("网络连接错误"),
      description: Text(message),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      icon: const Icon(Iconsax.wifi_square),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }

  // 显示未知错误提示
  static void showUnknownError(BuildContext context, String message) {
    toastification.show(
      context: context,
      type: ToastificationType.error,
      style: ToastificationStyle.flat,
      title: const Text("发生错误"),
      description: Text(message),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 3),
      icon: const Icon(Iconsax.information),
      borderRadius: BorderRadius.circular(12.0),
      boxShadow: lowModeShadow,
      applyBlurEffect: true,
    );
  }
}
