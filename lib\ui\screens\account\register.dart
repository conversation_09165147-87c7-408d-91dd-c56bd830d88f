import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/controllers/page/register_controller.dart';

class Register extends GetView<RegisterController> {
  const Register({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('注册账号'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 顶部Logo
                Center(
                  child: Container(
                    height: 100,
                    width: 100,
                    margin: const EdgeInsets.symmetric(vertical: 20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).shadowColor.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Image.asset(
                        "assets/emojis/cry.png",
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),

                // 标题
                Text(
                  "创建新账户",
                  style: Theme.of(context)
                      .textTheme
                      .headlineSmall
                      ?.copyWith(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  "请填写以下信息完成注册",
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // 邮箱输入框
                TextField(
                  controller: controller.emailController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.email_outlined),
                    hintText: '请输入您的电子邮箱',
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),

                // 密码输入框
                TextField(
                  controller: controller.passwordController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.lock_outline),
                    hintText: '请输入密码（8位以上）',
                  ),
                  obscureText: true,
                ),
                const SizedBox(height: 16),

                // 确认密码输入框
                TextField(
                  controller: controller.confirmPasswordController,
                  decoration: const InputDecoration(
                    prefixIcon: Icon(Icons.lock_outline),
                    hintText: '请再次输入密码',
                  ),
                  obscureText: true,
                ),
                const SizedBox(height: 16),

                // 验证码输入区域
                Row(
                  children: [
                    // 验证码输入框
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: controller.verificationCodeController,
                        decoration: const InputDecoration(
                          prefixIcon: Icon(Icons.verified_user_outlined),
                          hintText: '请输入验证码',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // 获取验证码按钮
                    Expanded(
                      flex: 1,
                      child: Obx(() => ElevatedButton(
                            onPressed: controller.isLoading.value
                                ? null
                                : controller.isSentCode.value
                                    ? null // 防止多次点击
                                    : controller.sendVerificationCode,
                            child: controller.isLoading.value
                                ? const Text('发送中')
                                : controller.isSentCode.value
                                    ? Text(controller.count.value.toString())
                                    : const Text('获取验证码'),
                          )),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // 错误消息
                Obx(() => controller.hasError.value
                    ? Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Text(
                          controller.errorMessage.value,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.error,
                            fontSize: 14,
                          ),
                        ),
                      )
                    : const SizedBox.shrink()),

                // 服务条款和隐私政策
                Row(
                  children: [
                    Obx(() => Checkbox(
                          value: controller.agreeToTerms.value,
                          onChanged: (value) =>
                              controller.agreeToTerms.value = value ?? false,
                        )),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => controller.agreeToTerms.value =
                            !controller.agreeToTerms.value,
                        child: MarkdownBody(
                            data:
                                "已阅读并且同意与你的 [服务条款](https://example.com/terms) 和 [隐私政策](https://example.com/privacy)",
                            onTapLink: (text, href, title) {
                              if (href != null) {
                                launchUrl(Uri.parse(href));
                              }
                            },
                            styleSheet:
                                MarkdownStyleSheet.fromTheme(Theme.of(context))
                                    .copyWith(
                              p: Theme.of(context).textTheme.bodyMedium,
                              a: TextStyle(
                                  color: Theme.of(context).colorScheme.primary),
                            )),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // 注册按钮
                Obx(() => ElevatedButton(
                      onPressed: controller.isLoading.value
                          ? null
                          : controller.register,
                      child: controller.isLoading.value
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text("注册"),
                    )),

                // 已有账号？登录
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        "已有账号？",
                        style: TextStyle(color: Colors.grey),
                      ),
                      GestureDetector(
                        onTap: () => Get.offNamed('/Login'),
                        child: const Text(
                          "登录",
                          style: TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
