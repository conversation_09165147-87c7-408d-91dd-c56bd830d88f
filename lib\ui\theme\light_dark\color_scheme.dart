import 'package:flutter/material.dart';

/// Light [ColorScheme] made with FlexColorScheme v8.2.0.
/// Requires Flutter 3.22.0 or later.
const ColorScheme lightColorScheme = ColorScheme(
  brightness: Brightness.light,
  primary: Color(0xFF171717),
  onPrimary: Color(0xFFFFFFFF),
  primaryContainer: Color(0xFFE2E2E2),
  onPrimaryContainer: Color(0xFF1B1B1B),
  primaryFixed: Color(0xFFE2E2E2),
  primaryFixedDim: Color(0xFFC6C6C6),
  onPrimaryFixed: Color(0xFF1B1B1B),
  onPrimaryFixedVariant: Color(0xFF474747),
  secondary: Color(0xFF5E5E5E),
  onSecondary: Color(0xFFFFFFFF),
  secondaryContainer: Color(0xFFE2E2E2),
  onSecondaryContainer: Color(0xFF1B1B1B),
  secondaryFixed: Color(0xFFE2E2E2),
  secondaryFixedDim: Color(0xFFC6C6C6),
  onSecondaryFixed: Color(0xFF1B1B1B),
  onSecondaryFixedVariant: Color(0xFF474747),
  tertiary: Color(0xFF5E5E5E),
  onTertiary: Color(0xFFFFFFFF),
  tertiaryContainer: Color(0xFFE2E2E2),
  onTertiaryContainer: Color(0xFF1B1B1B),
  tertiaryFixed: Color(0xFFE2E2E2),
  tertiaryFixedDim: Color(0xFFC6C6C6),
  onTertiaryFixed: Color(0xFF1B1B1B),
  onTertiaryFixedVariant: Color(0xFF474747),
  error: Color(0xFFB91A24),
  onError: Color(0xFFFFFFFF),
  errorContainer: Color(0xFFFFDAD7),
  onErrorContainer: Color(0xFF410004),
  surface: Color(0xFFF9F9F9),
  onSurface: Color(0xFF1B1B1B),
  surfaceDim: Color(0xFFDADADA),
  surfaceBright: Color(0xFFF9F9F9),
  surfaceContainerLowest: Color(0xFFFFFFFF),
  surfaceContainerLow: Color(0xFFF3F3F3),
  surfaceContainer: Color(0xFFEEEEEE),
  surfaceContainerHigh: Color(0xFFE8E8E8),
  surfaceContainerHighest: Color(0xFFE2E2E2),
  onSurfaceVariant: Color(0xFF474747),
  outline: Color(0xFF777777),
  outlineVariant: Color(0xFFC6C6C6),
  shadow: Color(0xFF000000),
  scrim: Color(0xFF000000),
  inverseSurface: Color(0xFF303030),
  onInverseSurface: Color(0xFFF1F1F1),
  inversePrimary: Color(0xFFC6C6C6),
  surfaceTint: Color(0xFF5E5E5E),
);

/// Dark [ColorScheme] made with FlexColorScheme v8.2.0.
/// Requires Flutter 3.22.0 or later.
const ColorScheme darkColorScheme = ColorScheme(
  brightness: Brightness.dark,
  primary: Color(0xFFC6C6C6),
  onPrimary: Color(0xFF303030),
  primaryContainer: Color(0xFF474747),
  onPrimaryContainer: Color(0xFFE2E2E2),
  primaryFixed: Color(0xFFE2E2E2),
  primaryFixedDim: Color(0xFFC6C6C6),
  onPrimaryFixed: Color(0xFF1B1B1B),
  onPrimaryFixedVariant: Color(0xFF474747),
  secondary: Color(0xFFC6C6C6),
  onSecondary: Color(0xFF303030),
  secondaryContainer: Color(0xFF474747),
  onSecondaryContainer: Color(0xFFE2E2E2),
  secondaryFixed: Color(0xFFE2E2E2),
  secondaryFixedDim: Color(0xFFC6C6C6),
  onSecondaryFixed: Color(0xFF1B1B1B),
  onSecondaryFixedVariant: Color(0xFF474747),
  tertiary: Color(0xFFC6C6C6),
  onTertiary: Color(0xFF303030),
  tertiaryContainer: Color(0xFF474747),
  onTertiaryContainer: Color(0xFFE2E2E2),
  tertiaryFixed: Color(0xFFE2E2E2),
  tertiaryFixedDim: Color(0xFFC6C6C6),
  onTertiaryFixed: Color(0xFF1B1B1B),
  onTertiaryFixedVariant: Color(0xFF474747),
  error: Color(0xFFFFB3AD),
  onError: Color(0xFF65080D),
  errorContainer: Color(0xFF852221),
  onErrorContainer: Color(0xFFFFDAD7),
  surface: Color(0xFF141414),
  onSurface: Color(0xFFE2E2E2),
  surfaceDim: Color(0xFF141414),
  surfaceBright: Color(0xFF3A3A3A),
  surfaceContainerLowest: Color(0xFF0F0F0F),
  surfaceContainerLow: Color(0xFF1C1C1C),
  surfaceContainer: Color(0xFF202020),
  surfaceContainerHigh: Color(0xFF2B2B2B),
  surfaceContainerHighest: Color(0xFF363636),
  onSurfaceVariant: Color(0xFFC6C6C6),
  outline: Color(0xFF919191),
  outlineVariant: Color(0xFF474747),
  shadow: Color(0xFF000000),
  scrim: Color(0xFF000000),
  inverseSurface: Color(0xFFE2E2E2),
  onInverseSurface: Color(0xFF303030),
  inversePrimary: Color(0xFF5E5E5E),
  surfaceTint: Color(0xFFC6C6C6),
);
