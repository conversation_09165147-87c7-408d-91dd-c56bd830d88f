{"buildFiles": ["D:\\APP\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\APP\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Code\\GitHub\\aichat\\android\\app\\.cxx\\RelWithDebInfo\\2r135j20\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\APP\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Code\\GitHub\\aichat\\android\\app\\.cxx\\RelWithDebInfo\\2r135j20\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}