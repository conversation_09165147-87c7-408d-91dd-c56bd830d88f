### auth_middleware.dart
这是一个设置中间件的文件，其中内容很简单。
也就是在进入到某个路由的时候，进行一些条件的判断以及检测，以确定是否跳转其他界面，或者进行一些其他操作。

```dart
  RouteSettings? redirect(String? route) {
    InitTask controller = Get.find();

    // 判断用户是否选择了默认界面，默认在打开一个界面的时候，都会初始化信息控制器。
    // 如果当前用户设置了自定义界面，那么就获取到该角色对应的路由“toPageName”，具体方式是获取到所有角色列表，再获取当前角色id，来取得该角色信息
    // 如果没有设置，那么就自动跳转到默认聊天界面“/Chat”
    return controller.isCurrentCharOnCustomPage.value
        ? RouteSettings(name: controller.characterSettings[controller.currentCharacterId.value]?.customPageName)
        : const RouteSettings(name: '/Chat');
  }
```

该路由的调用模式如下，表示只有在涉及到这个路由的时候，才会使用到该中间件：
```dart
    GetPage(
      name: '/Chat',
      page: () => const ChatPage(),
      // 这边调用
      middlewares: [AuthMiddleware()],
    ),
```