import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

import '../../../api/client.dart';
import '../../../data/const.dart';
import '../../../data/hive/curd.dart';
import '../../../data/models/api_defult_model.dart';
import '../../../ui/widgets/toast/toast.dart';
import '../character_controller.dart';

class LoginController extends GetxController {
  // 表单控制器
  final TextEditingController emailController = TextEditingController();
  final TextEditingController pwdController = TextEditingController();

  // 复选框状态
  final RxBool agreeToTerms = false.obs;

  // 登录状态
  final RxBool isLoading = false.obs;
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  // isLogin 代表登录状态，初始化时由本地状态中读取，用于中间件这种无法异步读取本地信息的地方使用
  final RxBool isLogin = false.obs;

  @override
  Future<void> onInit() async {
    // 从本地初始化一下登录的状态
    await Hive.openBox(ConstData.settingBox);
    Box settingBox = Hive.box(ConstData.settingBox);
    final value = await settingBox.get(ConstData.isLogin);
    if (value == null) {
      isLogin.value = false;
    } else {
      isLogin.value = value;
    }

    super.onInit();
  }

  // 表单验证
  bool validateEmail() {
    if (emailController.text.trim().isEmpty) {
      errorMessage.value = '请输入电子邮箱';
      hasError.value = true;
      return false;
    }

    // 简单的邮箱格式验证
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(emailController.text.trim())) {
      errorMessage.value = '请输入有效的电子邮箱';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 密码验证
  bool validatePwd() {
    if (pwdController.text.trim().isEmpty) {
      errorMessage.value = '请输入密码';
      hasError.value = true;
      return false;
    }

    if (pwdController.text.trim().length < 8) {
      errorMessage.value = '密码不可小于8位数';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 验证条款是否同意
  bool validateTerms() {
    if (!agreeToTerms.value) {
      errorMessage.value = '请同意服务条款和隐私政策';
      hasError.value = true;
      return false;
    }

    hasError.value = false;
    return true;
  }

  // 登录方法
  void login() async {
    // 验证邮箱和条款
    if (!validateEmail() || !validateTerms() || !validatePwd()) {
      return;
    }

    // 设置加载状态
    isLoading.value = true;
    hasError.value = false;

    try {
      // 登录操作
      Client client = Client();
      APIModel result = await client.login(
          emailController.text.trim(), pwdController.text.trim());

      // 结果处理
      if (result.isSuccess) {
        // 账号已注册，应该进行登录
        // 登录成功提示弹窗
        Toast.showSuccess('登录成功', '开始愉快的聊天吧');
        isLoading.value = false;
        Get.offAllNamed('/');
      }
    } catch (e) {
      // 处理错误
      hasError.value = true;
      errorMessage.value = '登录失败，请稍后重试';
    } finally {
      isLoading.value = false;
    }
  }

  // 跳转到注册页面
  void goToRegister() {
    Get.toNamed(ConstData.registerPage);
  }

  // 跳转到注册页面
  void goToForget() {
    Get.toNamed(ConstData.forgetPage);
  }

  // 退出登录
  void logout() {
    // 删除所有有关登录后储存的信息,以及还需要更改登录的状态
    cleanLogin();
    // 回到主页
    CharacterController initTask = Get.find();
    initTask.currentIndex.value = 0;
    Get.back();
  }

// @override
// void onClose() {
//   emailController.dispose();
//   super.onClose();
// }
}
